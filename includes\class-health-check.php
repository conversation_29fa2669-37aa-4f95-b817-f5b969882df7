<?php
/**
 * Plugin Health Check Class
 * 
 * Provides diagnostic information to help troubleshoot plugin issues
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Health_Check {
    
    /**
     * Run comprehensive health check
     */
    public static function run_health_check() {
        $results = array();
        
        // Check database tables
        $results['database'] = self::check_database_tables();
        
        // Check file permissions
        $results['files'] = self::check_file_permissions();
        
        // Check PHP requirements
        $results['php'] = self::check_php_requirements();
        
        // Check WordPress requirements
        $results['wordpress'] = self::check_wordpress_requirements();
        
        // Check for conflicts
        $results['conflicts'] = self::check_plugin_conflicts();
        
        return $results;
    }
    
    /**
     * Check if all required database tables exist
     */
    public static function check_database_tables() {
        global $wpdb;
        
        $required_tables = array(
            'dab_tables',
            'dab_fields', 
            'dab_forms',
            'dab_views',
            'dab_relationships'
        );
        
        $results = array();
        
        foreach ($required_tables as $table) {
            $full_table_name = $wpdb->prefix . $table;
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$full_table_name'");
            
            $results[$table] = array(
                'exists' => !empty($exists),
                'name' => $full_table_name
            );
        }
        
        return $results;
    }
    
    /**
     * Check file permissions
     */
    public static function check_file_permissions() {
        $plugin_dir = plugin_dir_path(dirname(__FILE__));
        
        $files_to_check = array(
            'db-app-builder.php',
            'includes/',
            'admin/',
            'assets/'
        );
        
        $results = array();
        
        foreach ($files_to_check as $file) {
            $full_path = $plugin_dir . $file;
            $results[$file] = array(
                'exists' => file_exists($full_path),
                'readable' => is_readable($full_path),
                'writable' => is_writable($full_path),
                'permissions' => file_exists($full_path) ? substr(sprintf('%o', fileperms($full_path)), -4) : 'N/A'
            );
        }
        
        return $results;
    }
    
    /**
     * Check PHP requirements
     */
    public static function check_php_requirements() {
        return array(
            'version' => phpversion(),
            'meets_minimum' => version_compare(phpversion(), '7.0', '>='),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'extensions' => array(
                'mysqli' => extension_loaded('mysqli'),
                'json' => extension_loaded('json'),
                'curl' => extension_loaded('curl')
            )
        );
    }
    
    /**
     * Check WordPress requirements
     */
    public static function check_wordpress_requirements() {
        global $wp_version;
        
        return array(
            'version' => $wp_version,
            'meets_minimum' => version_compare($wp_version, '5.0', '>='),
            'multisite' => is_multisite(),
            'debug_mode' => defined('WP_DEBUG') && WP_DEBUG,
            'memory_limit' => WP_MEMORY_LIMIT
        );
    }
    
    /**
     * Check for potential plugin conflicts
     */
    public static function check_plugin_conflicts() {
        $active_plugins = get_option('active_plugins', array());
        
        // Known conflicting plugins or patterns
        $potential_conflicts = array();
        
        foreach ($active_plugins as $plugin) {
            // Check for database-related plugins that might conflict
            if (strpos($plugin, 'database') !== false || 
                strpos($plugin, 'db-') !== false ||
                strpos($plugin, 'form') !== false) {
                $potential_conflicts[] = $plugin;
            }
        }
        
        return array(
            'total_active_plugins' => count($active_plugins),
            'potential_conflicts' => $potential_conflicts,
            'active_plugins' => $active_plugins
        );
    }
    
    /**
     * Get formatted health check report
     */
    public static function get_formatted_report() {
        $results = self::run_health_check();
        
        $report = "<div class='dab-health-check'>";
        $report .= "<h2>Database App Builder - Health Check Report</h2>";
        
        // Database Tables
        $report .= "<h3>Database Tables</h3>";
        $report .= "<table class='widefat'>";
        $report .= "<thead><tr><th>Table</th><th>Status</th><th>Full Name</th></tr></thead><tbody>";
        
        foreach ($results['database'] as $table => $info) {
            $status = $info['exists'] ? '✅ Exists' : '❌ Missing';
            $report .= "<tr><td>{$table}</td><td>{$status}</td><td>{$info['name']}</td></tr>";
        }
        
        $report .= "</tbody></table>";
        
        // PHP Requirements
        $report .= "<h3>PHP Environment</h3>";
        $php = $results['php'];
        $php_status = $php['meets_minimum'] ? '✅' : '❌';
        $report .= "<p>{$php_status} PHP Version: {$php['version']} (Minimum: 7.0)</p>";
        $report .= "<p>Memory Limit: {$php['memory_limit']}</p>";
        $report .= "<p>Max Execution Time: {$php['max_execution_time']}s</p>";
        
        // WordPress Requirements  
        $report .= "<h3>WordPress Environment</h3>";
        $wp = $results['wordpress'];
        $wp_status = $wp['meets_minimum'] ? '✅' : '❌';
        $report .= "<p>{$wp_status} WordPress Version: {$wp['version']} (Minimum: 5.0)</p>";
        $report .= "<p>Debug Mode: " . ($wp['debug_mode'] ? 'Enabled' : 'Disabled') . "</p>";
        $report .= "<p>Memory Limit: {$wp['memory_limit']}</p>";
        
        // File Permissions
        $report .= "<h3>File Permissions</h3>";
        $report .= "<table class='widefat'>";
        $report .= "<thead><tr><th>File/Directory</th><th>Exists</th><th>Readable</th><th>Writable</th><th>Permissions</th></tr></thead><tbody>";
        
        foreach ($results['files'] as $file => $info) {
            $exists = $info['exists'] ? '✅' : '❌';
            $readable = $info['readable'] ? '✅' : '❌';
            $writable = $info['writable'] ? '✅' : '❌';
            $report .= "<tr><td>{$file}</td><td>{$exists}</td><td>{$readable}</td><td>{$writable}</td><td>{$info['permissions']}</td></tr>";
        }
        
        $report .= "</tbody></table>";
        
        // Plugin Conflicts
        $report .= "<h3>Plugin Analysis</h3>";
        $conflicts = $results['conflicts'];
        $report .= "<p>Total Active Plugins: {$conflicts['total_active_plugins']}</p>";
        
        if (!empty($conflicts['potential_conflicts'])) {
            $report .= "<p><strong>Potential Conflicts:</strong></p>";
            $report .= "<ul>";
            foreach ($conflicts['potential_conflicts'] as $plugin) {
                $report .= "<li>{$plugin}</li>";
            }
            $report .= "</ul>";
        } else {
            $report .= "<p>✅ No obvious plugin conflicts detected</p>";
        }
        
        $report .= "</div>";
        
        return $report;
    }
    
    /**
     * Add admin menu for health check
     */
    public static function add_admin_menu() {
        add_submenu_page(
            'dab_dashboard',
            'Health Check',
            'Health Check',
            'manage_options',
            'dab_health_check',
            array(__CLASS__, 'admin_page')
        );
    }
    
    /**
     * Admin page for health check
     */
    public static function admin_page() {
        echo '<div class="wrap">';
        echo self::get_formatted_report();
        echo '</div>';
    }
}

// Initialize health check if admin
if (is_admin()) {
    add_action('admin_menu', array('DAB_Health_Check', 'add_admin_menu'), 99);
}
