<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Main WooCommerce Integration Class
 * 
 * Handles the core integration between Database App Builder and WooCommerce
 */
class DAB_WooCommerce_Integration {
    
    /**
     * Initialize the WooCommerce integration
     */
    public static function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array(__CLASS__, 'woocommerce_not_active_notice'));
            return;
        }

        // Initialize integration components
        add_action('init', array(__CLASS__, 'init_integration_components'));
        
        // Add WooCommerce field types to DAB
        add_filter('dab_field_types', array(__CLASS__, 'add_woocommerce_field_types'));
        
        // Add admin notices for successful integration
        add_action('admin_notices', array(__CLASS__, 'integration_success_notice'));
    }

    /**
     * Initialize integration components
     */
    public static function init_integration_components() {
        // This will be called after all plugins are loaded
        do_action('dab_woocommerce_integration_loaded');
    }

    /**
     * Add WooCommerce-specific field types
     */
    public static function add_woocommerce_field_types($field_types) {
        $field_types['wc_product_selector'] = __('WooCommerce Product Selector', 'db-app-builder');
        $field_types['wc_category_selector'] = __('WooCommerce Category Selector', 'db-app-builder');
        $field_types['wc_customer_selector'] = __('WooCommerce Customer Selector', 'db-app-builder');
        $field_types['wc_order_status'] = __('WooCommerce Order Status', 'db-app-builder');
        $field_types['wc_payment_method'] = __('WooCommerce Payment Method', 'db-app-builder');
        
        return $field_types;
    }

    /**
     * Notice when WooCommerce is not active
     */
    public static function woocommerce_not_active_notice() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>' . __('Database App Builder WooCommerce Integration', 'db-app-builder') . '</strong></p>';
        echo '<p>' . __('WooCommerce is not active. Please install and activate WooCommerce to use the WooCommerce integration features.', 'db-app-builder') . '</p>';
        echo '</div>';
    }

    /**
     * Success notice for integration
     */
    public static function integration_success_notice() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Only show on DAB pages
        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'dab_') === false) {
            return;
        }
        
        // Check if this is the first time seeing the integration
        $integration_notice_dismissed = get_option('dab_woocommerce_integration_notice_dismissed', false);
        if ($integration_notice_dismissed) {
            return;
        }
        
        echo '<div class="notice notice-success is-dismissible" data-dismissible="dab-woocommerce-integration">';
        echo '<p><strong>' . __('WooCommerce Integration Active!', 'db-app-builder') . '</strong></p>';
        echo '<p>' . __('Database App Builder is now integrated with WooCommerce. You can now create custom product fields, manage customer data, add order fields, and view sales dashboards.', 'db-app-builder') . '</p>';
        echo '<p><a href="' . admin_url('admin.php?page=dab_woocommerce_product_fields') . '" class="button button-primary">' . __('Get Started', 'db-app-builder') . '</a></p>';
        echo '</div>';
        
        // Add JavaScript to handle notice dismissal
        echo '<script>
        jQuery(document).ready(function($) {
            $(document).on("click", ".notice[data-dismissible=\'dab-woocommerce-integration\'] .notice-dismiss", function() {
                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "dab_dismiss_woocommerce_integration_notice",
                        nonce: "' . wp_create_nonce('dab_dismiss_notice') . '"
                    }
                });
            });
        });
        </script>';
        
        // Register AJAX handler for notice dismissal
        add_action('wp_ajax_dab_dismiss_woocommerce_integration_notice', array(__CLASS__, 'dismiss_integration_notice'));
    }

    /**
     * Dismiss integration notice
     */
    public static function dismiss_integration_notice() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_dismiss_notice')) {
            wp_die('Security check failed');
        }
        
        update_option('dab_woocommerce_integration_notice_dismissed', true);
        wp_die();
    }

    /**
     * Get WooCommerce product categories
     */
    public static function get_product_categories() {
        $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
        ));
        
        $category_options = array();
        if (!is_wp_error($categories)) {
            foreach ($categories as $category) {
                $category_options[$category->term_id] = $category->name;
            }
        }
        
        return $category_options;
    }

    /**
     * Get WooCommerce payment methods
     */
    public static function get_payment_methods() {
        $payment_gateways = WC()->payment_gateways->get_available_payment_gateways();
        $methods = array();
        
        foreach ($payment_gateways as $gateway) {
            $methods[$gateway->id] = $gateway->get_title();
        }
        
        return $methods;
    }

    /**
     * Get WooCommerce order statuses
     */
    public static function get_order_statuses() {
        return wc_get_order_statuses();
    }

    /**
     * Check if WooCommerce is active and compatible
     */
    public static function is_woocommerce_compatible() {
        if (!class_exists('WooCommerce')) {
            return false;
        }
        
        // Check WooCommerce version compatibility
        $wc_version = WC()->version;
        $min_version = '3.0.0';
        
        return version_compare($wc_version, $min_version, '>=');
    }

    /**
     * Get integration status
     */
    public static function get_integration_status() {
        return array(
            'woocommerce_active' => class_exists('WooCommerce'),
            'woocommerce_version' => class_exists('WooCommerce') ? WC()->version : null,
            'compatible' => self::is_woocommerce_compatible(),
            'product_fields_enabled' => class_exists('DAB_Product_Fields_Manager'),
            'customer_data_enabled' => class_exists('DAB_Customer_Data_Manager'),
            'order_fields_enabled' => class_exists('DAB_Order_Fields_Manager'),
            'sales_dashboard_enabled' => class_exists('DAB_Sales_Dashboard_Manager'),
        );
    }
}
