<?php
/**
 * Frontend Authentication
 *
 * Handles authentication checks and redirects for frontend users
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Frontend_Auth {

    /**
     * Initialize the class
     */
    public static function init() {
        // Add authentication checks
        add_action('template_redirect', array(__CLASS__, 'check_authentication'));
        
        // Add user info to frontend
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_auth_scripts'));
        
        // Clean up expired sessions
        add_action('wp_scheduled_delete', array(__CLASS__, 'cleanup_expired_sessions'));
    }

    /**
     * Check if user is authenticated for protected pages
     */
    public static function check_authentication() {
        global $post;
        
        if (!is_object($post)) {
            return;
        }

        // Check if current page requires authentication
        $protected_pages = array(
            'user-dashboard',
            'user-profile',
            'my-data'
        );

        $page_slug = $post->post_name;
        
        if (in_array($page_slug, $protected_pages) || self::has_protected_shortcode($post->post_content)) {
            $current_user = DAB_Frontend_User_Manager::get_current_user();
            
            if (!$current_user) {
                // Redirect to login page
                wp_redirect(home_url('/login/?redirect=' . urlencode($_SERVER['REQUEST_URI'])));
                exit;
            }
        }
    }

    /**
     * Check if content has protected shortcodes
     */
    private static function has_protected_shortcode($content) {
        $protected_shortcodes = array(
            'dab_user_dashboard',
            'dab_user_profile',
            'dab_user_data'
        );

        foreach ($protected_shortcodes as $shortcode) {
            if (has_shortcode($content, $shortcode)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Enqueue authentication scripts
     */
    public static function enqueue_auth_scripts() {
        // Only enqueue on pages that might need authentication
        if (is_page() || is_single()) {
            wp_enqueue_script('dab-frontend-auth', 
                plugin_dir_url(dirname(__FILE__)) . 'assets/js/frontend-auth.js', 
                array('jquery'), DAB_VERSION, true
            );

            // Get current user info
            $current_user = DAB_Frontend_User_Manager::get_current_user();
            
            wp_localize_script('dab-frontend-auth', 'dab_auth', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('dab_frontend_nonce'),
                'current_user' => $current_user ? array(
                    'id' => $current_user->id,
                    'username' => $current_user->username,
                    'email' => $current_user->email,
                    'first_name' => $current_user->first_name,
                    'last_name' => $current_user->last_name,
                    'role' => $current_user->role,
                    'email_verified' => $current_user->email_verified
                ) : null,
                'login_url' => home_url('/login/'),
                'dashboard_url' => home_url('/user-dashboard/'),
                'profile_url' => home_url('/user-profile/')
            ));
        }
    }

    /**
     * Check if current user is logged in
     */
    public static function is_user_logged_in() {
        return DAB_Frontend_User_Manager::get_current_user() !== false;
    }

    /**
     * Get current user ID
     */
    public static function get_current_user_id() {
        $user = DAB_Frontend_User_Manager::get_current_user();
        return $user ? $user->id : 0;
    }

    /**
     * Check if current user has specific role
     */
    public static function current_user_can($role) {
        $user = DAB_Frontend_User_Manager::get_current_user();
        
        if (!$user) {
            return false;
        }

        // Admin can do everything
        if ($user->role === 'admin') {
            return true;
        }

        return $user->role === $role;
    }

    /**
     * Require user to be logged in
     */
    public static function require_login() {
        if (!self::is_user_logged_in()) {
            wp_redirect(home_url('/login/?redirect=' . urlencode($_SERVER['REQUEST_URI'])));
            exit;
        }
    }

    /**
     * Require user to have specific role
     */
    public static function require_role($role) {
        self::require_login();
        
        if (!self::current_user_can($role)) {
            wp_die('You do not have permission to access this page.');
        }
    }

    /**
     * Clean up expired sessions
     */
    public static function cleanup_expired_sessions() {
        global $wpdb;
        
        $sessions_table = $wpdb->prefix . 'dab_user_sessions';
        
        $wpdb->query("DELETE FROM $sessions_table WHERE expires_at < NOW()");
    }

    /**
     * Get user by ID
     */
    public static function get_user_by_id($user_id) {
        global $wpdb;
        
        $users_table = $wpdb->prefix . 'dab_frontend_users';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $users_table WHERE id = %d AND status = 'active'",
            $user_id
        ));
    }

    /**
     * Update user meta
     */
    public static function update_user_meta($user_id, $meta_key, $meta_value) {
        global $wpdb;
        
        $meta_table = $wpdb->prefix . 'dab_user_meta';
        
        // Check if meta already exists
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM $meta_table WHERE user_id = %d AND meta_key = %s",
            $user_id, $meta_key
        ));

        if ($existing) {
            // Update existing meta
            return $wpdb->update($meta_table,
                array(
                    'meta_value' => maybe_serialize($meta_value),
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $existing->id)
            );
        } else {
            // Insert new meta
            return $wpdb->insert($meta_table, array(
                'user_id' => $user_id,
                'meta_key' => $meta_key,
                'meta_value' => maybe_serialize($meta_value)
            ));
        }
    }

    /**
     * Get user meta
     */
    public static function get_user_meta($user_id, $meta_key, $single = true) {
        global $wpdb;
        
        $meta_table = $wpdb->prefix . 'dab_user_meta';
        
        if ($single) {
            $meta_value = $wpdb->get_var($wpdb->prepare(
                "SELECT meta_value FROM $meta_table WHERE user_id = %d AND meta_key = %s",
                $user_id, $meta_key
            ));
            
            return $meta_value ? maybe_unserialize($meta_value) : '';
        } else {
            $meta_values = $wpdb->get_col($wpdb->prepare(
                "SELECT meta_value FROM $meta_table WHERE user_id = %d AND meta_key = %s",
                $user_id, $meta_key
            ));
            
            return array_map('maybe_unserialize', $meta_values);
        }
    }

    /**
     * Delete user meta
     */
    public static function delete_user_meta($user_id, $meta_key) {
        global $wpdb;
        
        $meta_table = $wpdb->prefix . 'dab_user_meta';
        
        return $wpdb->delete($meta_table, array(
            'user_id' => $user_id,
            'meta_key' => $meta_key
        ));
    }

    /**
     * Get all users with pagination
     */
    public static function get_users($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'role' => '',
            'status' => 'active',
            'search' => '',
            'orderby' => 'created_at',
            'order' => 'DESC',
            'limit' => 20,
            'offset' => 0
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $users_table = $wpdb->prefix . 'dab_frontend_users';
        $where = array("status = %s");
        $where_values = array($args['status']);
        
        if (!empty($args['role'])) {
            $where[] = "role = %s";
            $where_values[] = $args['role'];
        }
        
        if (!empty($args['search'])) {
            $where[] = "(username LIKE %s OR email LIKE %s OR first_name LIKE %s OR last_name LIKE %s)";
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values = array_merge($where_values, array($search_term, $search_term, $search_term, $search_term));
        }
        
        $where_clause = 'WHERE ' . implode(' AND ', $where);
        $order_clause = sprintf('ORDER BY %s %s', 
            sanitize_sql_orderby($args['orderby']), 
            $args['order'] === 'ASC' ? 'ASC' : 'DESC'
        );
        
        $limit_clause = sprintf('LIMIT %d OFFSET %d', $args['limit'], $args['offset']);
        
        $query = "SELECT * FROM $users_table $where_clause $order_clause $limit_clause";
        
        return $wpdb->get_results($wpdb->prepare($query, $where_values));
    }

    /**
     * Count users
     */
    public static function count_users($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'role' => '',
            'status' => 'active',
            'search' => ''
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $users_table = $wpdb->prefix . 'dab_frontend_users';
        $where = array("status = %s");
        $where_values = array($args['status']);
        
        if (!empty($args['role'])) {
            $where[] = "role = %s";
            $where_values[] = $args['role'];
        }
        
        if (!empty($args['search'])) {
            $where[] = "(username LIKE %s OR email LIKE %s OR first_name LIKE %s OR last_name LIKE %s)";
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values = array_merge($where_values, array($search_term, $search_term, $search_term, $search_term));
        }
        
        $where_clause = 'WHERE ' . implode(' AND ', $where);
        $query = "SELECT COUNT(*) FROM $users_table $where_clause";
        
        return $wpdb->get_var($wpdb->prepare($query, $where_values));
    }
}
