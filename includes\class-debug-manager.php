<?php
/**
 * Debug Manager Class
 *
 * Provides debugging functionality for the Database App Builder plugin.
 *
 * @since      1.0.0
 * @package    Database_Admin_Builder
 * @subpackage Database_Admin_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Debug_Manager {

    /**
     * The single instance of the class.
     *
     * @var DAB_Debug_Manager
     */
    protected static $_instance = null;

    /**
     * Debug log array
     *
     * @var array
     */
    private $debug_log = array();

    /**
     * Whether debug mode is enabled
     *
     * @var bool
     */
    private $debug_enabled = false;

    /**
     * Main DAB_Debug_Manager Instance.
     *
     * Ensures only one instance of DAB_Debug_Manager is loaded or can be loaded.
     *
     * @return DAB_Debug_Manager Main instance.
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor.
     */
    public function __construct() {
        // Check if debug mode is enabled
        $this->debug_enabled = get_option('dab_debug_mode', false);

        // Add hooks
        add_action('admin_init', array($this, 'register_settings'));

        // Add AJAX handler for viewing debug log
        add_action('wp_ajax_dab_view_debug_log', array($this, 'ajax_view_debug_log'));

        // Add filter to check if debug mode is enabled
        add_filter('dab_is_debug_enabled', array($this, 'is_debug_enabled'));
    }

    /**
     * Register settings.
     */
    public function register_settings() {
        register_setting('dab_license_settings', 'dab_debug_mode', array(
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean',
        ));
    }

    /**
     * Check if debug mode is enabled.
     *
     * @return bool Whether debug mode is enabled.
     */
    public function is_debug_enabled() {
        return $this->debug_enabled;
    }

    /**
     * Log debug message.
     *
     * @param string $message Debug message.
     * @param string $type Message type (info, warning, error).
     * @param array $context Additional context data.
     */
    public function log($message, $type = 'info', $context = array()) {
        if (!$this->debug_enabled && $type !== 'error') {
            return;
        }

        $log_entry = array(
            'time' => current_time('mysql'),
            'message' => $message,
            'type' => $type,
            'context' => $context,
        );

        // Add to internal log
        $this->debug_log[] = $log_entry;

        // Store in option (limited to last 100 entries)
        $stored_log = get_option('dab_debug_log', array());
        array_unshift($stored_log, $log_entry);
        $stored_log = array_slice($stored_log, 0, 100);
        update_option('dab_debug_log', $stored_log);

        // Also log to error log if WP_DEBUG is enabled
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $context_str = !empty($context) ? ' | Context: ' . json_encode($context) : '';
            error_log('DAB Debug [' . strtoupper($type) . ']: ' . $message . $context_str);
        }
    }

    /**
     * Log license activation attempt.
     *
     * @param string $license_key License key.
     * @param array $request_data Request data.
     */
    public function log_license_activation_attempt($license_key, $request_data) {
        $this->log(
            'License activation attempt',
            'info',
            array(
                'license_key' => $this->mask_license_key($license_key),
                'request_data' => $request_data,
            )
        );
    }

    /**
     * Log license activation response.
     *
     * @param mixed $response Response from license server.
     * @param string $license_key License key.
     */
    public function log_license_activation_response($response, $license_key) {
        $type = 'info';
        $message = 'License activation response';

        if (is_wp_error($response)) {
            $type = 'error';
            $message = 'License activation error: ' . $response->get_error_message();
            $context = array(
                'license_key' => $this->mask_license_key($license_key),
                'error_code' => $response->get_error_code(),
                'error_data' => $response->get_error_data(),
            );
        } else {
            $context = array(
                'license_key' => $this->mask_license_key($license_key),
                'response' => $response,
            );
        }

        $this->log($message, $type, $context);
    }

    /**
     * Mask license key for security.
     *
     * @param string $license_key License key.
     * @return string Masked license key.
     */
    public function mask_license_key($license_key) {
        if (strlen($license_key) <= 8) {
            return '****';
        }

        return substr($license_key, 0, 4) . '****' . substr($license_key, -4);
    }

    /**
     * Get debug log.
     *
     * @param int $limit Number of log entries to return.
     * @return array Debug log.
     */
    public function get_debug_log($limit = 50) {
        $stored_log = get_option('dab_debug_log', array());
        return array_slice($stored_log, 0, $limit);
    }

    /**
     * Clear debug log.
     */
    public function clear_debug_log() {
        $this->debug_log = array();
        update_option('dab_debug_log', array());
    }

    /**
     * AJAX handler for viewing debug log.
     */
    public function ajax_view_debug_log() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Permission denied'));
            return;
        }

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_debug_nonce')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
            return;
        }

        $action = isset($_POST['debug_action']) ? sanitize_text_field($_POST['debug_action']) : 'view';

        if ($action === 'clear') {
            $this->clear_debug_log();
            wp_send_json_success(array('message' => 'Debug log cleared'));
            return;
        }

        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 50;
        $log = $this->get_debug_log($limit);

        wp_send_json_success(array('log' => $log));
    }

    /**
     * Get formatted debug information HTML.
     *
     * @return string Debug information HTML.
     */
    public function get_debug_info_html() {
        if (!$this->debug_enabled) {
            return '';
        }

        $html = '<div class="dab-debug-info">';
        $html .= '<h3>' . __('Debug Information', 'db-app-builder') . '</h3>';

        // System info
        $html .= '<h4>' . __('System Information', 'db-app-builder') . '</h4>';
        $html .= '<ul>';
        $html .= '<li><strong>PHP Version:</strong> ' . phpversion() . '</li>';
        $html .= '<li><strong>WordPress Version:</strong> ' . get_bloginfo('version') . '</li>';
        $html .= '<li><strong>Plugin Version:</strong> ' . DAB_VERSION . '</li>';
        $html .= '<li><strong>Memory Limit:</strong> ' . WP_MEMORY_LIMIT . '</li>';
        $html .= '</ul>';

        // Recent log entries
        $log = $this->get_debug_log(10);
        $html .= '<h4>' . __('Recent Debug Log', 'db-app-builder') . '</h4>';

        if (empty($log)) {
            $html .= '<p>' . __('No log entries found.', 'db-app-builder') . '</p>';
        } else {
            $html .= '<table class="widefat">';
            $html .= '<thead><tr><th>Time</th><th>Type</th><th>Message</th></tr></thead>';
            $html .= '<tbody>';

            foreach ($log as $entry) {
                $type_class = 'dab-log-' . $entry['type'];
                $html .= '<tr class="' . $type_class . '">';
                $html .= '<td>' . $entry['time'] . '</td>';
                $html .= '<td>' . ucfirst($entry['type']) . '</td>';
                $html .= '<td>' . esc_html($entry['message']) . '</td>';
                $html .= '</tr>';
            }

            $html .= '</tbody></table>';
        }

        $html .= '<p><a href="#" class="button dab-view-full-log">' . __('View Full Log', 'db-app-builder') . '</a> ';
        $html .= '<a href="#" class="button dab-clear-log">' . __('Clear Log', 'db-app-builder') . '</a></p>';

        $html .= '</div>';

        return $html;
    }
}

// Function is now defined in the main plugin file
