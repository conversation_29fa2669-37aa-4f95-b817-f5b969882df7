<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * WooCommerce Advanced Checkout Manager
 * 
 * Manages advanced checkout features including multi-step checkout, conditional fields,
 * enhanced payment options, and checkout optimization
 */
class DAB_Advanced_Checkout_Manager {
    
    /**
     * Initialize the Advanced Checkout Manager
     */
    public static function init() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Multi-step checkout
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_checkout_scripts'));
        add_action('woocommerce_checkout_init', array(__CLASS__, 'init_multi_step_checkout'));
        
        // Conditional checkout fields
        add_filter('woocommerce_checkout_fields', array(__CLASS__, 'add_conditional_checkout_fields'));
        add_action('woocommerce_checkout_process', array(__CLASS__, 'validate_conditional_fields'));
        
        // Enhanced payment options
        add_action('woocommerce_review_order_before_payment', array(__CLASS__, 'add_payment_enhancements'));
        
        // Checkout optimization
        add_action('woocommerce_checkout_before_customer_details', array(__CLASS__, 'add_checkout_progress_bar'));
        add_action('woocommerce_checkout_after_customer_details', array(__CLASS__, 'add_checkout_summary_sidebar'));
        
        // Guest checkout enhancements
        add_action('woocommerce_checkout_billing', array(__CLASS__, 'enhance_guest_checkout'));
        
        // Checkout analytics
        add_action('woocommerce_checkout_order_processed', array(__CLASS__, 'track_checkout_completion'));
        add_action('wp_footer', array(__CLASS__, 'track_checkout_abandonment'));
        
        // AJAX handlers
        add_action('wp_ajax_dab_validate_checkout_step', array(__CLASS__, 'ajax_validate_checkout_step'));
        add_action('wp_ajax_nopriv_dab_validate_checkout_step', array(__CLASS__, 'ajax_validate_checkout_step'));
        add_action('wp_ajax_dab_save_checkout_progress', array(__CLASS__, 'ajax_save_checkout_progress'));
        add_action('wp_ajax_nopriv_dab_save_checkout_progress', array(__CLASS__, 'ajax_save_checkout_progress'));
        
        // Checkout field dependencies
        add_action('wp_footer', array(__CLASS__, 'add_field_dependency_scripts'));
    }

    /**
     * Create database tables for advanced checkout
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Checkout analytics table
        $checkout_analytics_table = $wpdb->prefix . 'dab_wc_checkout_analytics';
        
        $sql_analytics = "CREATE TABLE $checkout_analytics_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            session_id varchar(255) NOT NULL,
            user_id bigint(20) DEFAULT 0,
            step_name varchar(50) NOT NULL,
            step_number int(11) NOT NULL,
            time_spent int(11) DEFAULT 0,
            completed tinyint(1) DEFAULT 0,
            abandoned tinyint(1) DEFAULT 0,
            error_messages text,
            form_data longtext,
            user_agent text,
            ip_address varchar(45),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY session_id (session_id),
            KEY user_id (user_id),
            KEY step_name (step_name),
            KEY completed (completed),
            KEY abandoned (abandoned)
        ) $charset_collate;";
        
        // Checkout field configurations table
        $checkout_fields_table = $wpdb->prefix . 'dab_wc_checkout_fields';
        
        $sql_fields = "CREATE TABLE $checkout_fields_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            field_name varchar(255) NOT NULL,
            field_label varchar(255) NOT NULL,
            field_type varchar(50) NOT NULL,
            field_section varchar(50) NOT NULL,
            field_step int(11) DEFAULT 1,
            field_order int(11) DEFAULT 0,
            required tinyint(1) DEFAULT 0,
            conditional_logic longtext,
            validation_rules longtext,
            field_options longtext,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY field_name (field_name),
            KEY field_section (field_section),
            KEY field_step (field_step),
            KEY is_active (is_active)
        ) $charset_collate;";
        
        // Checkout configurations table
        $checkout_config_table = $wpdb->prefix . 'dab_wc_checkout_config';
        
        $sql_config = "CREATE TABLE $checkout_config_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            config_name varchar(255) NOT NULL,
            config_value longtext,
            config_type varchar(50) DEFAULT 'general',
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY config_name (config_name),
            KEY config_type (config_type),
            KEY is_active (is_active)
        ) $charset_collate;";
        
        // Abandoned cart recovery table
        $abandoned_cart_table = $wpdb->prefix . 'dab_wc_abandoned_carts';
        
        $sql_abandoned = "CREATE TABLE $abandoned_cart_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            session_id varchar(255) NOT NULL,
            user_id bigint(20) DEFAULT 0,
            email varchar(255),
            cart_data longtext,
            cart_total decimal(10,2) DEFAULT 0,
            checkout_step varchar(50),
            abandoned_at datetime DEFAULT CURRENT_TIMESTAMP,
            recovery_email_sent tinyint(1) DEFAULT 0,
            recovery_email_sent_at datetime,
            recovered tinyint(1) DEFAULT 0,
            recovered_at datetime,
            recovery_order_id bigint(20) DEFAULT 0,
            PRIMARY KEY (id),
            UNIQUE KEY session_id (session_id),
            KEY user_id (user_id),
            KEY email (email),
            KEY abandoned_at (abandoned_at),
            KEY recovered (recovered)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_analytics);
        dbDelta($sql_fields);
        dbDelta($sql_config);
        dbDelta($sql_abandoned);
        
        // Create default configurations
        self::create_default_configurations();
    }

    /**
     * Create default checkout configurations
     */
    public static function create_default_configurations() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_checkout_config';
        
        $default_configs = array(
            array(
                'config_name' => 'multi_step_enabled',
                'config_value' => '1',
                'config_type' => 'general'
            ),
            array(
                'config_name' => 'checkout_steps',
                'config_value' => serialize(array(
                    1 => array('name' => 'Customer Information', 'fields' => array('billing')),
                    2 => array('name' => 'Shipping Information', 'fields' => array('shipping')),
                    3 => array('name' => 'Payment Information', 'fields' => array('payment')),
                    4 => array('name' => 'Review & Complete', 'fields' => array('review'))
                )),
                'config_type' => 'steps'
            ),
            array(
                'config_name' => 'progress_bar_enabled',
                'config_value' => '1',
                'config_type' => 'ui'
            ),
            array(
                'config_name' => 'auto_save_enabled',
                'config_value' => '1',
                'config_type' => 'features'
            ),
            array(
                'config_name' => 'abandoned_cart_recovery',
                'config_value' => '1',
                'config_type' => 'features'
            )
        );
        
        foreach ($default_configs as $config) {
            $existing = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM $table_name WHERE config_name = %s",
                    $config['config_name']
                )
            );
            
            if (!$existing) {
                $wpdb->insert($table_name, $config);
            }
        }
    }

    /**
     * Enqueue checkout scripts and styles
     */
    public static function enqueue_checkout_scripts() {
        if (is_checkout()) {
            wp_enqueue_script(
                'dab-advanced-checkout',
                plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/advanced-checkout.js',
                array('jquery', 'wc-checkout'),
                DAB_VERSION,
                true
            );
            
            wp_enqueue_style(
                'dab-advanced-checkout',
                plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/css/advanced-checkout.css',
                array(),
                DAB_VERSION
            );
            
            wp_localize_script('dab-advanced-checkout', 'dabAdvancedCheckout', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('dab_checkout_nonce'),
                'multiStepEnabled' => self::get_config('multi_step_enabled'),
                'autoSaveEnabled' => self::get_config('auto_save_enabled'),
                'steps' => self::get_checkout_steps(),
                'i18n' => array(
                    'next' => __('Next Step', 'db-app-builder'),
                    'previous' => __('Previous Step', 'db-app-builder'),
                    'complete' => __('Complete Order', 'db-app-builder'),
                    'validationError' => __('Please correct the errors before proceeding.', 'db-app-builder'),
                    'saving' => __('Saving...', 'db-app-builder'),
                    'saved' => __('Progress saved', 'db-app-builder')
                )
            ));
        }
    }

    /**
     * Initialize multi-step checkout
     */
    public static function init_multi_step_checkout() {
        if (!self::get_config('multi_step_enabled')) {
            return;
        }
        
        // Add checkout step tracking
        add_action('woocommerce_checkout_before_customer_details', array(__CLASS__, 'start_checkout_step_tracking'));
    }

    /**
     * Add checkout progress bar
     */
    public static function add_checkout_progress_bar() {
        if (!self::get_config('progress_bar_enabled') || !self::get_config('multi_step_enabled')) {
            return;
        }
        
        $steps = self::get_checkout_steps();
        $current_step = self::get_current_checkout_step();
        
        echo '<div class="dab-checkout-progress-bar">';
        echo '<div class="dab-progress-steps">';
        
        foreach ($steps as $step_number => $step_data) {
            $step_class = 'dab-progress-step';
            if ($step_number < $current_step) {
                $step_class .= ' completed';
            } elseif ($step_number == $current_step) {
                $step_class .= ' active';
            }
            
            echo '<div class="' . esc_attr($step_class) . '" data-step="' . esc_attr($step_number) . '">';
            echo '<div class="step-number">' . esc_html($step_number) . '</div>';
            echo '<div class="step-name">' . esc_html($step_data['name']) . '</div>';
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
    }

    /**
     * Add conditional checkout fields
     */
    public static function add_conditional_checkout_fields($fields) {
        $conditional_fields = self::get_conditional_fields();
        
        foreach ($conditional_fields as $field_config) {
            $section = $field_config->field_section . '_fields';
            
            if (!isset($fields[$section])) {
                continue;
            }
            
            $field_definition = array(
                'label' => $field_config->field_label,
                'required' => (bool) $field_config->required,
                'class' => array('form-row-wide', 'dab-conditional-field'),
                'priority' => 100 + $field_config->field_order,
                'custom_attributes' => array()
            );
            
            // Add conditional logic attributes
            if ($field_config->conditional_logic) {
                $logic = maybe_unserialize($field_config->conditional_logic);
                $field_definition['custom_attributes']['data-conditional-logic'] = json_encode($logic);
            }
            
            // Set field type
            switch ($field_config->field_type) {
                case 'select':
                    $field_definition['type'] = 'select';
                    $options = maybe_unserialize($field_config->field_options);
                    if (!empty($options['options'])) {
                        $field_definition['options'] = array('' => __('Select an option', 'db-app-builder')) + $options['options'];
                    }
                    break;
                    
                case 'textarea':
                    $field_definition['type'] = 'textarea';
                    break;
                    
                case 'checkbox':
                    $field_definition['type'] = 'checkbox';
                    break;
                    
                default:
                    $field_definition['type'] = 'text';
                    break;
            }
            
            $fields[$section][$field_config->field_name] = $field_definition;
        }
        
        return $fields;
    }

    /**
     * Track checkout completion
     */
    public static function track_checkout_completion($order_id) {
        $session_id = WC()->session->get_customer_id();
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_wc_checkout_analytics';
        
        // Update all steps for this session as completed
        $wpdb->update(
            $table_name,
            array('completed' => 1, 'updated_at' => current_time('mysql')),
            array('session_id' => $session_id),
            array('%d', '%s'),
            array('%s')
        );
        
        // Mark any abandoned cart as recovered
        $abandoned_table = $wpdb->prefix . 'dab_wc_abandoned_carts';
        $wpdb->update(
            $abandoned_table,
            array(
                'recovered' => 1,
                'recovered_at' => current_time('mysql'),
                'recovery_order_id' => $order_id
            ),
            array('session_id' => $session_id),
            array('%d', '%s', '%d'),
            array('%s')
        );
    }

    /**
     * Get checkout configuration
     */
    public static function get_config($config_name) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_checkout_config';
        
        $config_value = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT config_value FROM $table_name WHERE config_name = %s AND is_active = 1",
                $config_name
            )
        );
        
        if ($config_value !== null) {
            // Try to unserialize if it's serialized data
            $unserialized = maybe_unserialize($config_value);
            return $unserialized !== false ? $unserialized : $config_value;
        }
        
        return null;
    }

    /**
     * Get checkout steps configuration
     */
    public static function get_checkout_steps() {
        $steps = self::get_config('checkout_steps');
        
        if (!$steps) {
            // Default steps if not configured
            return array(
                1 => array('name' => __('Customer Information', 'db-app-builder'), 'fields' => array('billing')),
                2 => array('name' => __('Shipping Information', 'db-app-builder'), 'fields' => array('shipping')),
                3 => array('name' => __('Payment Information', 'db-app-builder'), 'fields' => array('payment')),
                4 => array('name' => __('Review & Complete', 'db-app-builder'), 'fields' => array('review'))
            );
        }
        
        return $steps;
    }

    /**
     * Get current checkout step
     */
    public static function get_current_checkout_step() {
        // This would be determined by JavaScript and stored in session/cookie
        // For now, return step 1 as default
        return 1;
    }

    /**
     * Get conditional fields
     */
    public static function get_conditional_fields() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_checkout_fields';
        
        return $wpdb->get_results(
            "SELECT * FROM $table_name WHERE is_active = 1 ORDER BY field_step ASC, field_order ASC"
        );
    }

    /**
     * AJAX handler for validating checkout step
     */
    public static function ajax_validate_checkout_step() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_checkout_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $step = intval($_POST['step']);
        $form_data = $_POST['form_data'];
        
        // Validate the step data
        $validation_errors = self::validate_step_data($step, $form_data);
        
        if (empty($validation_errors)) {
            // Save progress
            self::save_checkout_progress($step, $form_data);
            wp_send_json_success(array('message' => __('Step validated successfully', 'db-app-builder')));
        } else {
            wp_send_json_error(array('errors' => $validation_errors));
        }
    }

    /**
     * Validate step data
     */
    public static function validate_step_data($step, $form_data) {
        $errors = array();
        
        // Get step configuration
        $steps = self::get_checkout_steps();
        if (!isset($steps[$step])) {
            $errors[] = __('Invalid step', 'db-app-builder');
            return $errors;
        }
        
        // Validate required fields for this step
        $step_config = $steps[$step];
        $conditional_fields = self::get_conditional_fields();
        
        foreach ($conditional_fields as $field) {
            if ($field->field_step == $step && $field->required) {
                $field_value = isset($form_data[$field->field_name]) ? $form_data[$field->field_name] : '';
                
                if (empty($field_value)) {
                    $errors[] = sprintf(__('%s is required', 'db-app-builder'), $field->field_label);
                }
            }
        }
        
        return $errors;
    }

    /**
     * Save checkout progress
     */
    public static function save_checkout_progress($step, $form_data) {
        global $wpdb;
        
        $session_id = WC()->session->get_customer_id();
        $table_name = $wpdb->prefix . 'dab_wc_checkout_analytics';
        
        $wpdb->replace(
            $table_name,
            array(
                'session_id' => $session_id,
                'user_id' => get_current_user_id(),
                'step_name' => 'step_' . $step,
                'step_number' => $step,
                'form_data' => serialize($form_data),
                'updated_at' => current_time('mysql')
            ),
            array('%s', '%d', '%s', '%d', '%s', '%s')
        );
    }
}
