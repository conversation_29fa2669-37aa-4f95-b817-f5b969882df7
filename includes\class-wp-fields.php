<?php
/**
 * WordPress Role and User Fields
 * 
 * This class handles the WordPress role and user field types.
 */

if (!defined('ABSPATH')) exit;

class DAB_WP_Fields {
    /**
     * Initialize the class
     */
    public function __construct() {
        // Add filter to add the new field types
        add_filter('dab_field_types', array($this, 'add_wp_field_types'));
        
        // Add action to render the role field
        add_action('dab_render_field_wp_role', array($this, 'render_wp_role_field'), 10, 2);
        
        // Add action to render the user field
        add_action('dab_render_field_wp_user', array($this, 'render_wp_user_field'), 10, 2);
        
        // Add AJAX action to get users by role
        add_action('wp_ajax_dab_get_users_by_role', array($this, 'get_users_by_role'));
        add_action('wp_ajax_nopriv_dab_get_users_by_role', array($this, 'get_users_by_role'));
        
        // Enqueue scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Add WordPress field types to the list of available field types
     * 
     * @param array $field_types Existing field types
     * @return array Modified field types
     */
    public function add_wp_field_types($field_types) {
        $field_types['wp_role'] = __('WordPress Role', 'db-app-builder');
        $field_types['wp_user'] = __('WordPress User', 'db-app-builder');
        
        return $field_types;
    }
    
    /**
     * Enqueue scripts for WordPress fields
     */
    public function enqueue_scripts() {
        wp_enqueue_script(
            'dab-wp-fields', 
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/wp-fields.js', 
            array('jquery'), 
            DAB_VERSION, 
            true
        );
        
        wp_localize_script('dab-wp-fields', 'dabWpFields', array(
            'ajaxurl' => admin_url('ajax-url.php'),
            'nonce' => wp_create_nonce('dab_wp_fields_nonce'),
            'i18n' => array(
                'selectRole' => __('Select Role', 'db-app-builder'),
                'selectUser' => __('Select User', 'db-app-builder'),
                'loading' => __('Loading...', 'db-app-builder'),
                'noUsers' => __('No users found for this role', 'db-app-builder'),
            )
        ));
    }
    
    /**
     * Render WordPress role field
     * 
     * @param object $field Field object
     * @param string $value Current value
     */
    public function render_wp_role_field($field, $value = '') {
        $field_id = 'dab-field-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);
        $required = !empty($field->required) ? ' required' : '';
        
        // Get all WordPress roles
        $roles = $this->get_wp_roles();
        
        echo '<select id="' . $field_id . '" name="' . $field_name . '" class="dab-wp-role-field"' . $required . '>';
        echo '<option value="">' . __('Select Role', 'db-app-builder') . '</option>';
        
        foreach ($roles as $role_key => $role_name) {
            $selected = selected($value, $role_key, false);
            echo '<option value="' . esc_attr($role_key) . '"' . $selected . '>' . esc_html($role_name) . '</option>';
        }
        
        echo '</select>';
    }
    
    /**
     * Render WordPress user field
     * 
     * @param object $field Field object
     * @param string $value Current value
     */
    public function render_wp_user_field($field, $value = '') {
        $field_id = 'dab-field-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);
        $required = !empty($field->required) ? ' required' : '';
        
        // Get field options
        $filter_by_role = !empty($field->wp_user_role_filter);
        $role_field = !empty($field->wp_user_role_field) ? $field->wp_user_role_field : '';
        
        echo '<div class="dab-wp-user-field-container">';
        
        // If filtering by role field, add data attributes
        $data_attrs = '';
        if ($filter_by_role && !empty($role_field)) {
            $data_attrs = ' data-filter-by-role="1" data-role-field="' . esc_attr($role_field) . '"';
        }
        
        echo '<select id="' . $field_id . '" name="' . $field_name . '" class="dab-wp-user-field"' . $required . $data_attrs . '>';
        echo '<option value="">' . __('Select User', 'db-app-builder') . '</option>';
        
        // If not filtering by role, show all users
        if (!$filter_by_role) {
            $users = $this->get_wp_users();
            foreach ($users as $user) {
                $selected = selected($value, $user->ID, false);
                echo '<option value="' . esc_attr($user->ID) . '"' . $selected . '>' . esc_html($user->display_name) . ' (' . esc_html($user->user_email) . ')</option>';
            }
        } elseif (!empty($value)) {
            // If filtering by role but we have a value, get the user
            $user = get_user_by('id', $value);
            if ($user) {
                echo '<option value="' . esc_attr($user->ID) . '" selected>' . esc_html($user->display_name) . ' (' . esc_html($user->user_email) . ')</option>';
            }
        }
        
        echo '</select>';
        echo '</div>';
    }
    
    /**
     * Get WordPress roles
     * 
     * @return array Array of role keys and names
     */
    public function get_wp_roles() {
        global $wp_roles;
        
        if (!isset($wp_roles)) {
            $wp_roles = new WP_Roles();
        }
        
        return $wp_roles->get_names();
    }
    
    /**
     * Get WordPress users
     * 
     * @param string $role Optional role to filter by
     * @return array Array of user objects
     */
    public function get_wp_users($role = '') {
        $args = array(
            'number' => 100, // Limit to 100 users for performance
            'orderby' => 'display_name',
            'order' => 'ASC',
        );
        
        if (!empty($role)) {
            $args['role'] = $role;
        }
        
        return get_users($args);
    }
    
    /**
     * AJAX handler to get users by role
     */
    public function get_users_by_role() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_wp_fields_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'db-app-builder')));
        }
        
        $role = isset($_POST['role']) ? sanitize_text_field($_POST['role']) : '';
        
        $users = $this->get_wp_users($role);
        
        $options = array();
        foreach ($users as $user) {
            $options[] = array(
                'id' => $user->ID,
                'text' => $user->display_name . ' (' . $user->user_email . ')',
            );
        }
        
        wp_send_json_success($options);
    }
}

// Initialize the class
new DAB_WP_Fields();
