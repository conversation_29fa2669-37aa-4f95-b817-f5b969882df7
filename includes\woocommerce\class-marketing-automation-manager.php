<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * WooCommerce Marketing Automation Manager
 * 
 * Manages marketing automation features including abandoned cart recovery,
 * customer retention campaigns, product recommendations, and loyalty programs
 */
class DAB_Marketing_Automation_Manager {
    
    /**
     * Initialize the Marketing Automation Manager
     */
    public static function init() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Abandoned cart recovery
        add_action('woocommerce_cart_updated', array(__CLASS__, 'track_cart_activity'));
        add_action('wp_logout', array(__CLASS__, 'handle_cart_abandonment'));
        add_action('dab_check_abandoned_carts', array(__CLASS__, 'process_abandoned_carts'));
        
        // Customer lifecycle automation
        add_action('woocommerce_created_customer', array(__CLASS__, 'trigger_welcome_sequence'));
        add_action('woocommerce_order_status_completed', array(__CLASS__, 'trigger_post_purchase_sequence'));
        add_action('dab_customer_retention_check', array(__CLASS__, 'process_retention_campaigns'));
        
        // Product recommendations
        add_action('woocommerce_single_product_summary', array(__CLASS__, 'display_product_recommendations'), 25);
        add_action('woocommerce_cart_collaterals', array(__CLASS__, 'display_cart_recommendations'));
        add_action('woocommerce_checkout_after_order_review', array(__CLASS__, 'display_checkout_recommendations'));
        
        // Email automation
        add_action('dab_send_marketing_emails', array(__CLASS__, 'process_email_queue'));
        
        // Loyalty program
        add_action('woocommerce_order_status_completed', array(__CLASS__, 'award_loyalty_points'));
        add_action('woocommerce_checkout_order_processed', array(__CLASS__, 'apply_loyalty_discount'));
        
        // Scheduled tasks
        if (!wp_next_scheduled('dab_check_abandoned_carts')) {
            wp_schedule_event(time(), 'hourly', 'dab_check_abandoned_carts');
        }
        if (!wp_next_scheduled('dab_customer_retention_check')) {
            wp_schedule_event(time(), 'daily', 'dab_customer_retention_check');
        }
        if (!wp_next_scheduled('dab_send_marketing_emails')) {
            wp_schedule_event(time(), 'every_five_minutes', 'dab_send_marketing_emails');
        }
        
        // AJAX handlers
        add_action('wp_ajax_dab_create_campaign', array(__CLASS__, 'ajax_create_campaign'));
        add_action('wp_ajax_dab_get_campaign_stats', array(__CLASS__, 'ajax_get_campaign_stats'));
        add_action('wp_ajax_dab_send_test_email', array(__CLASS__, 'ajax_send_test_email'));
    }

    /**
     * Create database tables for marketing automation
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Marketing campaigns table
        $campaigns_table = $wpdb->prefix . 'dab_wc_marketing_campaigns';
        
        $sql_campaigns = "CREATE TABLE $campaigns_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            campaign_name varchar(255) NOT NULL,
            campaign_type varchar(50) NOT NULL,
            trigger_event varchar(100) NOT NULL,
            trigger_conditions longtext,
            email_template_id mediumint(9),
            delay_hours int(11) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            send_limit int(11) DEFAULT 0,
            priority int(11) DEFAULT 10,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY campaign_type (campaign_type),
            KEY trigger_event (trigger_event),
            KEY is_active (is_active)
        ) $charset_collate;";
        
        // Email templates table
        $templates_table = $wpdb->prefix . 'dab_wc_email_templates';
        
        $sql_templates = "CREATE TABLE $templates_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            template_name varchar(255) NOT NULL,
            template_type varchar(50) NOT NULL,
            subject_line varchar(255) NOT NULL,
            email_content longtext NOT NULL,
            template_variables longtext,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY template_type (template_type),
            KEY is_active (is_active)
        ) $charset_collate;";
        
        // Email queue table
        $email_queue_table = $wpdb->prefix . 'dab_wc_email_queue';
        
        $sql_email_queue = "CREATE TABLE $email_queue_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            campaign_id mediumint(9),
            template_id mediumint(9),
            recipient_email varchar(255) NOT NULL,
            recipient_name varchar(255),
            user_id bigint(20) DEFAULT 0,
            subject_line varchar(255) NOT NULL,
            email_content longtext NOT NULL,
            scheduled_at datetime NOT NULL,
            sent_at datetime,
            status varchar(20) DEFAULT 'pending',
            error_message text,
            open_count int(11) DEFAULT 0,
            click_count int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY campaign_id (campaign_id),
            KEY template_id (template_id),
            KEY recipient_email (recipient_email),
            KEY scheduled_at (scheduled_at),
            KEY status (status)
        ) $charset_collate;";
        
        // Product recommendations table
        $recommendations_table = $wpdb->prefix . 'dab_wc_product_recommendations';
        
        $sql_recommendations = "CREATE TABLE $recommendations_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) DEFAULT 0,
            session_id varchar(255),
            product_id bigint(20) NOT NULL,
            recommended_product_id bigint(20) NOT NULL,
            recommendation_type varchar(50) NOT NULL,
            score decimal(5,2) DEFAULT 0,
            context varchar(100),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY session_id (session_id),
            KEY product_id (product_id),
            KEY recommendation_type (recommendation_type)
        ) $charset_collate;";
        
        // Loyalty points table
        $loyalty_table = $wpdb->prefix . 'dab_wc_loyalty_points';
        
        $sql_loyalty = "CREATE TABLE $loyalty_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            points_earned int(11) NOT NULL,
            points_spent int(11) DEFAULT 0,
            points_balance int(11) NOT NULL,
            transaction_type varchar(50) NOT NULL,
            order_id bigint(20) DEFAULT 0,
            description text,
            expiry_date date,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY transaction_type (transaction_type),
            KEY order_id (order_id),
            KEY expiry_date (expiry_date)
        ) $charset_collate;";
        
        // Campaign statistics table
        $stats_table = $wpdb->prefix . 'dab_wc_campaign_stats';
        
        $sql_stats = "CREATE TABLE $stats_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            campaign_id mediumint(9) NOT NULL,
            stat_date date NOT NULL,
            emails_sent int(11) DEFAULT 0,
            emails_opened int(11) DEFAULT 0,
            emails_clicked int(11) DEFAULT 0,
            conversions int(11) DEFAULT 0,
            revenue_generated decimal(10,2) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY campaign_date (campaign_id, stat_date),
            KEY campaign_id (campaign_id),
            KEY stat_date (stat_date)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_campaigns);
        dbDelta($sql_templates);
        dbDelta($sql_email_queue);
        dbDelta($sql_recommendations);
        dbDelta($sql_loyalty);
        dbDelta($sql_stats);
        
        // Create default campaigns and templates
        self::create_default_campaigns();
        self::create_default_email_templates();
    }

    /**
     * Create default marketing campaigns
     */
    public static function create_default_campaigns() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_marketing_campaigns';
        
        $default_campaigns = array(
            array(
                'campaign_name' => 'Welcome New Customers',
                'campaign_type' => 'welcome',
                'trigger_event' => 'customer_registered',
                'delay_hours' => 1,
                'priority' => 10
            ),
            array(
                'campaign_name' => 'Abandoned Cart Recovery - 1 Hour',
                'campaign_type' => 'abandoned_cart',
                'trigger_event' => 'cart_abandoned',
                'delay_hours' => 1,
                'priority' => 20
            ),
            array(
                'campaign_name' => 'Abandoned Cart Recovery - 24 Hours',
                'campaign_type' => 'abandoned_cart',
                'trigger_event' => 'cart_abandoned',
                'delay_hours' => 24,
                'priority' => 15
            ),
            array(
                'campaign_name' => 'Post-Purchase Thank You',
                'campaign_type' => 'post_purchase',
                'trigger_event' => 'order_completed',
                'delay_hours' => 2,
                'priority' => 30
            ),
            array(
                'campaign_name' => 'Win-Back Inactive Customers',
                'campaign_type' => 'retention',
                'trigger_event' => 'customer_inactive',
                'delay_hours' => 0,
                'priority' => 5
            )
        );
        
        foreach ($default_campaigns as $campaign) {
            $existing = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM $table_name WHERE campaign_name = %s",
                    $campaign['campaign_name']
                )
            );
            
            if (!$existing) {
                $wpdb->insert($table_name, $campaign);
            }
        }
    }

    /**
     * Create default email templates
     */
    public static function create_default_email_templates() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_email_templates';
        
        $default_templates = array(
            array(
                'template_name' => 'Welcome Email',
                'template_type' => 'welcome',
                'subject_line' => 'Welcome to {{site_name}}!',
                'email_content' => '<h2>Welcome {{customer_name}}!</h2><p>Thank you for joining {{site_name}}. We\'re excited to have you as part of our community.</p><p>Start shopping now and enjoy exclusive offers!</p>',
                'template_variables' => serialize(array('customer_name', 'site_name'))
            ),
            array(
                'template_name' => 'Abandoned Cart Recovery',
                'template_type' => 'abandoned_cart',
                'subject_line' => 'You left something in your cart!',
                'email_content' => '<h2>Don\'t forget your items!</h2><p>Hi {{customer_name}},</p><p>You left some great items in your cart. Complete your purchase now before they\'re gone!</p>{{cart_items}}<p><a href="{{checkout_url}}" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none;">Complete Purchase</a></p>',
                'template_variables' => serialize(array('customer_name', 'cart_items', 'checkout_url'))
            ),
            array(
                'template_name' => 'Post-Purchase Thank You',
                'template_type' => 'post_purchase',
                'subject_line' => 'Thank you for your order #{{order_number}}',
                'email_content' => '<h2>Thank you for your purchase!</h2><p>Hi {{customer_name}},</p><p>Your order #{{order_number}} has been completed. We appreciate your business!</p><p>Don\'t forget to leave a review and check out our other products.</p>',
                'template_variables' => serialize(array('customer_name', 'order_number'))
            )
        );
        
        foreach ($default_templates as $template) {
            $existing = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM $table_name WHERE template_name = %s",
                    $template['template_name']
                )
            );
            
            if (!$existing) {
                $wpdb->insert($table_name, $template);
            }
        }
    }

    /**
     * Track cart activity for abandonment detection
     */
    public static function track_cart_activity() {
        if (!WC()->cart || WC()->cart->is_empty()) {
            return;
        }
        
        $session_id = WC()->session->get_customer_id();
        $user_id = get_current_user_id();
        $cart_data = WC()->cart->get_cart();
        $cart_total = WC()->cart->get_cart_contents_total();
        
        // Update or create cart tracking record
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_wc_abandoned_carts';
        
        $wpdb->replace(
            $table_name,
            array(
                'session_id' => $session_id,
                'user_id' => $user_id,
                'email' => self::get_customer_email(),
                'cart_data' => serialize($cart_data),
                'cart_total' => $cart_total,
                'abandoned_at' => current_time('mysql')
            ),
            array('%s', '%d', '%s', '%s', '%f', '%s')
        );
    }

    /**
     * Process abandoned carts
     */
    public static function process_abandoned_carts() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_abandoned_carts';
        $campaigns_table = $wpdb->prefix . 'dab_wc_marketing_campaigns';
        
        // Get abandoned carts older than 1 hour
        $abandoned_carts = $wpdb->get_results(
            "SELECT * FROM $table_name 
             WHERE abandoned_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)
             AND recovered = 0 
             AND email IS NOT NULL 
             AND email != ''"
        );
        
        foreach ($abandoned_carts as $cart) {
            // Get applicable campaigns
            $campaigns = $wpdb->get_results(
                "SELECT * FROM $campaigns_table 
                 WHERE campaign_type = 'abandoned_cart' 
                 AND is_active = 1 
                 ORDER BY priority DESC"
            );
            
            foreach ($campaigns as $campaign) {
                $hours_since_abandonment = (time() - strtotime($cart->abandoned_at)) / 3600;
                
                if ($hours_since_abandonment >= $campaign->delay_hours) {
                    self::queue_campaign_email($campaign, $cart);
                }
            }
        }
    }

    /**
     * Queue campaign email
     */
    public static function queue_campaign_email($campaign, $cart_data) {
        global $wpdb;
        
        $template_id = $campaign->email_template_id;
        if (!$template_id) {
            return;
        }
        
        $template = self::get_email_template($template_id);
        if (!$template) {
            return;
        }
        
        $email_content = self::process_email_template($template, $cart_data);
        $subject_line = self::process_template_variables($template->subject_line, $cart_data);
        
        $queue_table = $wpdb->prefix . 'dab_wc_email_queue';
        
        $wpdb->insert(
            $queue_table,
            array(
                'campaign_id' => $campaign->id,
                'template_id' => $template_id,
                'recipient_email' => $cart_data->email,
                'user_id' => $cart_data->user_id,
                'subject_line' => $subject_line,
                'email_content' => $email_content,
                'scheduled_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%d', '%s', '%s', '%s')
        );
    }

    /**
     * Process email queue
     */
    public static function process_email_queue() {
        global $wpdb;
        
        $queue_table = $wpdb->prefix . 'dab_wc_email_queue';
        
        // Get pending emails
        $pending_emails = $wpdb->get_results(
            "SELECT * FROM $queue_table 
             WHERE status = 'pending' 
             AND scheduled_at <= NOW() 
             ORDER BY scheduled_at ASC 
             LIMIT 10"
        );
        
        foreach ($pending_emails as $email) {
            $sent = wp_mail(
                $email->recipient_email,
                $email->subject_line,
                $email->email_content,
                array('Content-Type: text/html; charset=UTF-8')
            );
            
            if ($sent) {
                $wpdb->update(
                    $queue_table,
                    array(
                        'status' => 'sent',
                        'sent_at' => current_time('mysql')
                    ),
                    array('id' => $email->id),
                    array('%s', '%s'),
                    array('%d')
                );
            } else {
                $wpdb->update(
                    $queue_table,
                    array(
                        'status' => 'failed',
                        'error_message' => 'Failed to send email'
                    ),
                    array('id' => $email->id),
                    array('%s', '%s'),
                    array('%d')
                );
            }
        }
    }

    /**
     * Get customer email
     */
    public static function get_customer_email() {
        if (is_user_logged_in()) {
            $user = wp_get_current_user();
            return $user->user_email;
        }
        
        // Try to get from checkout fields
        if (WC()->customer) {
            return WC()->customer->get_billing_email();
        }
        
        return '';
    }

    /**
     * Get email template
     */
    public static function get_email_template($template_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_email_templates';
        
        return $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE id = %d AND is_active = 1",
                $template_id
            )
        );
    }

    /**
     * Process email template with variables
     */
    public static function process_email_template($template, $data) {
        $content = $template->email_content;
        
        // Replace template variables
        $content = self::process_template_variables($content, $data);
        
        return $content;
    }

    /**
     * Process template variables
     */
    public static function process_template_variables($content, $data) {
        // Common variables
        $content = str_replace('{{site_name}}', get_bloginfo('name'), $content);
        $content = str_replace('{{site_url}}', home_url(), $content);
        
        // Customer-specific variables
        if (isset($data->user_id) && $data->user_id) {
            $user = get_user_by('id', $data->user_id);
            if ($user) {
                $content = str_replace('{{customer_name}}', $user->display_name, $content);
            }
        }
        
        if (isset($data->email)) {
            $content = str_replace('{{customer_email}}', $data->email, $content);
        }
        
        // Cart-specific variables
        if (isset($data->cart_data)) {
            $cart_items_html = self::generate_cart_items_html($data->cart_data);
            $content = str_replace('{{cart_items}}', $cart_items_html, $content);
        }
        
        $content = str_replace('{{checkout_url}}', wc_get_checkout_url(), $content);
        
        return $content;
    }

    /**
     * Generate cart items HTML
     */
    public static function generate_cart_items_html($cart_data) {
        if (!$cart_data) {
            return '';
        }
        
        $cart_items = maybe_unserialize($cart_data);
        if (!$cart_items) {
            return '';
        }
        
        $html = '<div class="cart-items">';
        
        foreach ($cart_items as $cart_item) {
            $product = wc_get_product($cart_item['product_id']);
            if (!$product) {
                continue;
            }
            
            $html .= '<div class="cart-item">';
            $html .= '<h4>' . $product->get_name() . '</h4>';
            $html .= '<p>Quantity: ' . $cart_item['quantity'] . '</p>';
            $html .= '<p>Price: ' . wc_price($product->get_price()) . '</p>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}
