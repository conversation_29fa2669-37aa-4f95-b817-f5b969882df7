<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * WooCommerce Enhanced Reporting Manager
 * 
 * Manages advanced reporting features including custom reports, data visualization,
 * automated report generation, and business intelligence dashboards
 */
class DAB_Enhanced_Reporting_Manager {
    
    /**
     * Initialize the Enhanced Reporting Manager
     */
    public static function init() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Report generation
        add_action('admin_menu', array(__CLASS__, 'add_reports_menu'));
        add_action('wp_ajax_dab_generate_report', array(__CLASS__, 'ajax_generate_report'));
        add_action('wp_ajax_dab_export_report', array(__CLASS__, 'ajax_export_report'));
        
        // Scheduled reports
        add_action('dab_generate_scheduled_reports', array(__CLASS__, 'generate_scheduled_reports'));
        if (!wp_next_scheduled('dab_generate_scheduled_reports')) {
            wp_schedule_event(time(), 'daily', 'dab_generate_scheduled_reports');
        }
        
        // Data aggregation
        add_action('dab_aggregate_report_data', array(__CLASS__, 'aggregate_daily_data'));
        if (!wp_next_scheduled('dab_aggregate_report_data')) {
            wp_schedule_event(time(), 'daily', 'dab_aggregate_report_data');
        }
        
        // Real-time analytics
        add_action('woocommerce_checkout_order_processed', array(__CLASS__, 'track_order_analytics'));
        add_action('woocommerce_add_to_cart', array(__CLASS__, 'track_cart_analytics'));
        add_action('wp_footer', array(__CLASS__, 'track_page_analytics'));
        
        // Custom report types
        add_filter('dab_report_types', array(__CLASS__, 'register_report_types'));
        
        // Dashboard widgets
        add_action('wp_dashboard_setup', array(__CLASS__, 'add_dashboard_widgets'));
        
        // Enqueue scripts and styles
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_admin_assets'));
    }

    /**
     * Create database tables for enhanced reporting
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Custom reports table
        $reports_table = $wpdb->prefix . 'dab_wc_custom_reports';
        
        $sql_reports = "CREATE TABLE $reports_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            report_name varchar(255) NOT NULL,
            report_type varchar(50) NOT NULL,
            report_config longtext,
            report_query longtext,
            chart_config longtext,
            filters longtext,
            schedule_frequency varchar(20),
            last_generated datetime,
            is_active tinyint(1) DEFAULT 1,
            created_by bigint(20),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY report_type (report_type),
            KEY is_active (is_active),
            KEY created_by (created_by)
        ) $charset_collate;";
        
        // Report data cache table
        $report_data_table = $wpdb->prefix . 'dab_wc_report_data';
        
        $sql_report_data = "CREATE TABLE $report_data_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            report_id mediumint(9) NOT NULL,
            data_key varchar(255) NOT NULL,
            data_value longtext,
            data_type varchar(50) DEFAULT 'json',
            date_range_start date,
            date_range_end date,
            generated_at datetime DEFAULT CURRENT_TIMESTAMP,
            expires_at datetime,
            PRIMARY KEY (id),
            UNIQUE KEY report_data_key (report_id, data_key, date_range_start, date_range_end),
            KEY report_id (report_id),
            KEY generated_at (generated_at),
            KEY expires_at (expires_at)
        ) $charset_collate;";
        
        // Analytics events table
        $analytics_table = $wpdb->prefix . 'dab_wc_analytics_events';
        
        $sql_analytics = "CREATE TABLE $analytics_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            event_type varchar(50) NOT NULL,
            event_data longtext,
            user_id bigint(20) DEFAULT 0,
            session_id varchar(255),
            product_id bigint(20) DEFAULT 0,
            order_id bigint(20) DEFAULT 0,
            page_url varchar(500),
            user_agent text,
            ip_address varchar(45),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY event_type (event_type),
            KEY user_id (user_id),
            KEY session_id (session_id),
            KEY product_id (product_id),
            KEY order_id (order_id),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // Aggregated data table
        $aggregated_table = $wpdb->prefix . 'dab_wc_aggregated_data';
        
        $sql_aggregated = "CREATE TABLE $aggregated_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            metric_name varchar(100) NOT NULL,
            metric_value decimal(15,4) NOT NULL,
            metric_count int(11) DEFAULT 1,
            dimension_1 varchar(100),
            dimension_2 varchar(100),
            dimension_3 varchar(100),
            date_recorded date NOT NULL,
            period_type varchar(20) DEFAULT 'daily',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY metric_dimensions_date (metric_name, dimension_1, dimension_2, dimension_3, date_recorded, period_type),
            KEY metric_name (metric_name),
            KEY date_recorded (date_recorded),
            KEY period_type (period_type)
        ) $charset_collate;";
        
        // Report subscriptions table
        $subscriptions_table = $wpdb->prefix . 'dab_wc_report_subscriptions';
        
        $sql_subscriptions = "CREATE TABLE $subscriptions_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            report_id mediumint(9) NOT NULL,
            user_id bigint(20) NOT NULL,
            email varchar(255) NOT NULL,
            frequency varchar(20) NOT NULL,
            format varchar(20) DEFAULT 'pdf',
            last_sent datetime,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY report_user (report_id, user_id),
            KEY report_id (report_id),
            KEY user_id (user_id),
            KEY frequency (frequency),
            KEY is_active (is_active)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_reports);
        dbDelta($sql_report_data);
        dbDelta($sql_analytics);
        dbDelta($sql_aggregated);
        dbDelta($sql_subscriptions);
        
        // Create default reports
        self::create_default_reports();
    }

    /**
     * Create default reports
     */
    public static function create_default_reports() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_custom_reports';
        
        $default_reports = array(
            array(
                'report_name' => 'Sales Performance Dashboard',
                'report_type' => 'sales_dashboard',
                'report_config' => serialize(array(
                    'metrics' => array('total_sales', 'order_count', 'average_order_value', 'conversion_rate'),
                    'chart_types' => array('line', 'bar', 'pie'),
                    'date_ranges' => array('7days', '30days', '90days', 'custom')
                )),
                'schedule_frequency' => 'daily'
            ),
            array(
                'report_name' => 'Product Performance Report',
                'report_type' => 'product_performance',
                'report_config' => serialize(array(
                    'metrics' => array('units_sold', 'revenue', 'profit_margin', 'inventory_turnover'),
                    'grouping' => array('product', 'category', 'brand'),
                    'sorting' => array('revenue_desc', 'units_desc', 'profit_desc')
                )),
                'schedule_frequency' => 'weekly'
            ),
            array(
                'report_name' => 'Customer Analytics Report',
                'report_type' => 'customer_analytics',
                'report_config' => serialize(array(
                    'metrics' => array('new_customers', 'returning_customers', 'customer_lifetime_value', 'churn_rate'),
                    'segments' => array('vip', 'regular', 'new', 'inactive'),
                    'cohort_analysis' => true
                )),
                'schedule_frequency' => 'monthly'
            ),
            array(
                'report_name' => 'Inventory Management Report',
                'report_type' => 'inventory_report',
                'report_config' => serialize(array(
                    'metrics' => array('stock_levels', 'low_stock_alerts', 'out_of_stock', 'reorder_points'),
                    'alerts' => array('low_stock', 'overstock', 'fast_moving', 'slow_moving')
                )),
                'schedule_frequency' => 'daily'
            ),
            array(
                'report_name' => 'Marketing Campaign Performance',
                'report_type' => 'marketing_performance',
                'report_config' => serialize(array(
                    'metrics' => array('email_open_rate', 'click_through_rate', 'conversion_rate', 'roi'),
                    'campaigns' => array('abandoned_cart', 'welcome', 'retention', 'promotional'),
                    'attribution' => true
                )),
                'schedule_frequency' => 'weekly'
            )
        );
        
        foreach ($default_reports as $report) {
            $existing = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM $table_name WHERE report_name = %s",
                    $report['report_name']
                )
            );
            
            if (!$existing) {
                $report['created_by'] = get_current_user_id();
                $wpdb->insert($table_name, $report);
            }
        }
    }

    /**
     * Register report types
     */
    public static function register_report_types($types) {
        $types['sales_dashboard'] = __('Sales Performance Dashboard', 'db-app-builder');
        $types['product_performance'] = __('Product Performance Report', 'db-app-builder');
        $types['customer_analytics'] = __('Customer Analytics Report', 'db-app-builder');
        $types['inventory_report'] = __('Inventory Management Report', 'db-app-builder');
        $types['marketing_performance'] = __('Marketing Campaign Performance', 'db-app-builder');
        $types['financial_summary'] = __('Financial Summary Report', 'db-app-builder');
        $types['conversion_funnel'] = __('Conversion Funnel Analysis', 'db-app-builder');
        $types['cohort_analysis'] = __('Customer Cohort Analysis', 'db-app-builder');
        
        return $types;
    }

    /**
     * Track order analytics
     */
    public static function track_order_analytics($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }
        
        $event_data = array(
            'order_total' => $order->get_total(),
            'order_items_count' => $order->get_item_count(),
            'payment_method' => $order->get_payment_method(),
            'customer_id' => $order->get_customer_id(),
            'billing_country' => $order->get_billing_country(),
            'shipping_method' => $order->get_shipping_method()
        );
        
        self::track_analytics_event('order_completed', $event_data, $order->get_customer_id(), $order_id);
        
        // Aggregate daily sales data
        self::aggregate_metric('daily_sales', $order->get_total(), array(
            'payment_method' => $order->get_payment_method(),
            'country' => $order->get_billing_country()
        ));
    }

    /**
     * Track cart analytics
     */
    public static function track_cart_analytics($cart_item_key) {
        if (!WC()->cart) {
            return;
        }
        
        $cart_item = WC()->cart->get_cart_item($cart_item_key);
        if (!$cart_item) {
            return;
        }
        
        $product = $cart_item['data'];
        $event_data = array(
            'product_id' => $cart_item['product_id'],
            'variation_id' => $cart_item['variation_id'],
            'quantity' => $cart_item['quantity'],
            'price' => $product->get_price(),
            'category' => wp_get_post_terms($cart_item['product_id'], 'product_cat', array('fields' => 'names'))
        );
        
        self::track_analytics_event('add_to_cart', $event_data, get_current_user_id(), 0, $cart_item['product_id']);
    }

    /**
     * Track analytics event
     */
    public static function track_analytics_event($event_type, $event_data, $user_id = 0, $order_id = 0, $product_id = 0) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_analytics_events';
        
        $wpdb->insert(
            $table_name,
            array(
                'event_type' => $event_type,
                'event_data' => serialize($event_data),
                'user_id' => $user_id,
                'session_id' => WC()->session ? WC()->session->get_customer_id() : '',
                'product_id' => $product_id,
                'order_id' => $order_id,
                'page_url' => $_SERVER['REQUEST_URI'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'ip_address' => self::get_client_ip()
            ),
            array('%s', '%s', '%d', '%s', '%d', '%d', '%s', '%s', '%s')
        );
    }

    /**
     * Aggregate metric data
     */
    public static function aggregate_metric($metric_name, $value, $dimensions = array()) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_aggregated_data';
        $today = current_time('Y-m-d');
        
        $dimension_1 = isset($dimensions[0]) ? $dimensions[0] : (isset($dimensions['dimension_1']) ? $dimensions['dimension_1'] : null);
        $dimension_2 = isset($dimensions[1]) ? $dimensions[1] : (isset($dimensions['dimension_2']) ? $dimensions['dimension_2'] : null);
        $dimension_3 = isset($dimensions[2]) ? $dimensions[2] : (isset($dimensions['dimension_3']) ? $dimensions['dimension_3'] : null);
        
        // Check if record exists
        $existing = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name 
                 WHERE metric_name = %s 
                 AND dimension_1 = %s 
                 AND dimension_2 = %s 
                 AND dimension_3 = %s 
                 AND date_recorded = %s 
                 AND period_type = 'daily'",
                $metric_name,
                $dimension_1,
                $dimension_2,
                $dimension_3,
                $today
            )
        );
        
        if ($existing) {
            // Update existing record
            $wpdb->update(
                $table_name,
                array(
                    'metric_value' => $existing->metric_value + $value,
                    'metric_count' => $existing->metric_count + 1,
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $existing->id),
                array('%f', '%d', '%s'),
                array('%d')
            );
        } else {
            // Insert new record
            $wpdb->insert(
                $table_name,
                array(
                    'metric_name' => $metric_name,
                    'metric_value' => $value,
                    'metric_count' => 1,
                    'dimension_1' => $dimension_1,
                    'dimension_2' => $dimension_2,
                    'dimension_3' => $dimension_3,
                    'date_recorded' => $today,
                    'period_type' => 'daily'
                ),
                array('%s', '%f', '%d', '%s', '%s', '%s', '%s', '%s')
            );
        }
    }

    /**
     * Generate report data
     */
    public static function generate_report_data($report_id, $date_range = '30days') {
        global $wpdb;
        
        $reports_table = $wpdb->prefix . 'dab_wc_custom_reports';
        $report = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $reports_table WHERE id = %d", $report_id)
        );
        
        if (!$report) {
            return false;
        }
        
        $report_config = maybe_unserialize($report->report_config);
        $report_data = array();
        
        switch ($report->report_type) {
            case 'sales_dashboard':
                $report_data = self::generate_sales_dashboard_data($report_config, $date_range);
                break;
                
            case 'product_performance':
                $report_data = self::generate_product_performance_data($report_config, $date_range);
                break;
                
            case 'customer_analytics':
                $report_data = self::generate_customer_analytics_data($report_config, $date_range);
                break;
                
            case 'inventory_report':
                $report_data = self::generate_inventory_report_data($report_config, $date_range);
                break;
                
            case 'marketing_performance':
                $report_data = self::generate_marketing_performance_data($report_config, $date_range);
                break;
        }
        
        // Cache the report data
        self::cache_report_data($report_id, $report_data, $date_range);
        
        return $report_data;
    }

    /**
     * Generate sales dashboard data
     */
    public static function generate_sales_dashboard_data($config, $date_range) {
        global $wpdb;
        
        $date_condition = self::get_date_condition($date_range);
        
        // Total sales
        $total_sales = $wpdb->get_var(
            "SELECT SUM(CAST(pm.meta_value AS DECIMAL(10,2))) 
             FROM {$wpdb->posts} p 
             INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
             WHERE p.post_type = 'shop_order' 
             AND p.post_status IN ('wc-completed', 'wc-processing') 
             AND pm.meta_key = '_order_total' 
             $date_condition"
        );
        
        // Order count
        $order_count = $wpdb->get_var(
            "SELECT COUNT(*) 
             FROM {$wpdb->posts} p 
             WHERE p.post_type = 'shop_order' 
             AND p.post_status IN ('wc-completed', 'wc-processing') 
             $date_condition"
        );
        
        // Average order value
        $avg_order_value = $order_count > 0 ? ($total_sales / $order_count) : 0;
        
        return array(
            'total_sales' => $total_sales ?: 0,
            'order_count' => $order_count ?: 0,
            'average_order_value' => $avg_order_value,
            'conversion_rate' => self::calculate_conversion_rate($date_range)
        );
    }

    /**
     * Get date condition for SQL queries
     */
    public static function get_date_condition($date_range) {
        switch ($date_range) {
            case '7days':
                return "AND p.post_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)";
            case '30days':
                return "AND p.post_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
            case '90days':
                return "AND p.post_date >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
            case 'this_month':
                return "AND YEAR(p.post_date) = YEAR(CURDATE()) AND MONTH(p.post_date) = MONTH(CURDATE())";
            case 'last_month':
                return "AND YEAR(p.post_date) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)) AND MONTH(p.post_date) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))";
            default:
                return "AND p.post_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
        }
    }

    /**
     * Calculate conversion rate
     */
    public static function calculate_conversion_rate($date_range) {
        global $wpdb;
        
        $analytics_table = $wpdb->prefix . 'dab_wc_analytics_events';
        $date_condition = str_replace('p.post_date', 'created_at', self::get_date_condition($date_range));
        
        // Get unique sessions that added to cart
        $cart_sessions = $wpdb->get_var(
            "SELECT COUNT(DISTINCT session_id) 
             FROM $analytics_table 
             WHERE event_type = 'add_to_cart' 
             $date_condition"
        );
        
        // Get unique sessions that completed orders
        $order_sessions = $wpdb->get_var(
            "SELECT COUNT(DISTINCT session_id) 
             FROM $analytics_table 
             WHERE event_type = 'order_completed' 
             $date_condition"
        );
        
        return $cart_sessions > 0 ? ($order_sessions / $cart_sessions) * 100 : 0;
    }

    /**
     * Get client IP address
     */
    public static function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Cache report data
     */
    public static function cache_report_data($report_id, $data, $date_range) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_report_data';
        $cache_key = 'report_' . $date_range;
        
        // Set expiry time (1 hour for most reports)
        $expires_at = date('Y-m-d H:i:s', time() + 3600);
        
        $wpdb->replace(
            $table_name,
            array(
                'report_id' => $report_id,
                'data_key' => $cache_key,
                'data_value' => serialize($data),
                'data_type' => 'json',
                'generated_at' => current_time('mysql'),
                'expires_at' => $expires_at
            ),
            array('%d', '%s', '%s', '%s', '%s', '%s')
        );
    }
}
