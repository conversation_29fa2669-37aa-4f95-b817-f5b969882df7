<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * WooCommerce Order Fields Manager
 * 
 * Manages custom fields for WooCommerce orders
 */
class DAB_Order_Fields_Manager {
    
    /**
     * Initialize the Order Fields Manager
     */
    public static function init() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Add checkout fields
        add_action('woocommerce_checkout_fields', array(__CLASS__, 'add_checkout_fields'));
        
        // Save checkout fields
        add_action('woocommerce_checkout_update_order_meta', array(__CLASS__, 'save_checkout_fields'));
        
        // Display fields in order admin
        add_action('woocommerce_admin_order_data_after_billing_address', array(__CLASS__, 'display_order_fields_admin'));
        
        // Display fields in order emails
        add_action('woocommerce_email_order_meta', array(__CLASS__, 'display_order_fields_email'), 10, 3);
        
        // Display fields in order details (frontend)
        add_action('woocommerce_order_details_after_order_table', array(__CLASS__, 'display_order_fields_frontend'));
        
        // Add order meta box
        add_action('add_meta_boxes', array(__CLASS__, 'add_order_meta_boxes'));
        
        // Save order meta box
        add_action('save_post', array(__CLASS__, 'save_order_meta_box'));
        
        // Handle AJAX requests
        add_action('wp_ajax_dab_get_order_fields', array(__CLASS__, 'ajax_get_order_fields'));
        add_action('wp_ajax_dab_save_order_field_config', array(__CLASS__, 'ajax_save_order_field_config'));
    }

    /**
     * Create database tables for order fields
     */
    public static function create_tables() {
        global $wpdb;
        
        // Order fields configuration table
        $table_name = $wpdb->prefix . 'dab_wc_order_fields';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            field_name varchar(255) NOT NULL,
            field_label varchar(255) NOT NULL,
            field_type varchar(50) NOT NULL,
            field_options longtext,
            field_order int(11) DEFAULT 0,
            required tinyint(1) DEFAULT 0,
            show_in_checkout tinyint(1) DEFAULT 1,
            show_in_admin tinyint(1) DEFAULT 1,
            show_in_email tinyint(1) DEFAULT 1,
            show_in_frontend tinyint(1) DEFAULT 1,
            field_section varchar(50) DEFAULT 'billing',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY field_name (field_name)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Order field values table
        $values_table = $wpdb->prefix . 'dab_wc_order_field_values';
        
        $sql_values = "CREATE TABLE $values_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            field_name varchar(255) NOT NULL,
            field_value longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY order_field (order_id, field_name),
            KEY order_id (order_id),
            KEY field_name (field_name)
        ) $charset_collate;";
        
        dbDelta($sql_values);
        
        // Insert default order fields
        self::insert_default_order_fields();
    }

    /**
     * Insert default order fields
     */
    public static function insert_default_order_fields() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_order_fields';
        
        $default_fields = array(
            array(
                'field_name' => 'delivery_instructions',
                'field_label' => __('Delivery Instructions', 'db-app-builder'),
                'field_type' => 'textarea',
                'field_section' => 'shipping',
                'show_in_checkout' => 1,
                'show_in_admin' => 1,
                'show_in_email' => 1,
                'show_in_frontend' => 1
            ),
            array(
                'field_name' => 'preferred_delivery_time',
                'field_label' => __('Preferred Delivery Time', 'db-app-builder'),
                'field_type' => 'select',
                'field_options' => serialize(array(
                    'options' => array(
                        'morning' => __('Morning (9AM - 12PM)', 'db-app-builder'),
                        'afternoon' => __('Afternoon (12PM - 5PM)', 'db-app-builder'),
                        'evening' => __('Evening (5PM - 8PM)', 'db-app-builder')
                    )
                )),
                'field_section' => 'shipping',
                'show_in_checkout' => 1,
                'show_in_admin' => 1,
                'show_in_email' => 1,
                'show_in_frontend' => 1
            ),
            array(
                'field_name' => 'gift_message',
                'field_label' => __('Gift Message', 'db-app-builder'),
                'field_type' => 'textarea',
                'field_section' => 'order',
                'show_in_checkout' => 1,
                'show_in_admin' => 1,
                'show_in_email' => 1,
                'show_in_frontend' => 1
            ),
            array(
                'field_name' => 'purchase_order_number',
                'field_label' => __('Purchase Order Number', 'db-app-builder'),
                'field_type' => 'text',
                'field_section' => 'billing',
                'show_in_checkout' => 1,
                'show_in_admin' => 1,
                'show_in_email' => 1,
                'show_in_frontend' => 1
            )
        );
        
        foreach ($default_fields as $field) {
            $existing = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM $table_name WHERE field_name = %s",
                    $field['field_name']
                )
            );
            
            if (!$existing) {
                $wpdb->insert($table_name, $field);
            }
        }
    }

    /**
     * Add checkout fields
     */
    public static function add_checkout_fields($fields) {
        $field_configs = self::get_order_field_configs();
        
        foreach ($field_configs as $field_config) {
            if (!$field_config->show_in_checkout) {
                continue;
            }
            
            $field_name = $field_config->field_name;
            $field_section = $field_config->field_section;
            
            // Determine which section to add the field to
            $section_key = $field_section . '_fields';
            if (!isset($fields[$section_key])) {
                $section_key = 'billing_fields'; // fallback
            }
            
            $field_definition = array(
                'label' => $field_config->field_label,
                'required' => (bool) $field_config->required,
                'class' => array('form-row-wide'),
                'priority' => 100 + $field_config->field_order
            );
            
            // Add field-specific properties
            switch ($field_config->field_type) {
                case 'textarea':
                    $field_definition['type'] = 'textarea';
                    $field_definition['class'] = array('form-row-wide');
                    break;
                    
                case 'select':
                    $field_definition['type'] = 'select';
                    $field_options = maybe_unserialize($field_config->field_options);
                    if (!empty($field_options['options'])) {
                        $field_definition['options'] = array('' => __('Select an option', 'db-app-builder')) + $field_options['options'];
                    }
                    break;
                    
                case 'checkbox':
                    $field_definition['type'] = 'checkbox';
                    break;
                    
                case 'date':
                    $field_definition['type'] = 'date';
                    break;
                    
                default:
                    $field_definition['type'] = 'text';
                    break;
            }
            
            $fields[$section_key][$field_name] = $field_definition;
        }
        
        return $fields;
    }

    /**
     * Save checkout fields
     */
    public static function save_checkout_fields($order_id) {
        $field_configs = self::get_order_field_configs();
        
        foreach ($field_configs as $field_config) {
            if (!$field_config->show_in_checkout) {
                continue;
            }
            
            $field_name = $field_config->field_name;
            
            if (isset($_POST[$field_name])) {
                $field_value = sanitize_text_field($_POST[$field_name]);
                self::save_order_field_value($order_id, $field_name, $field_value);
                
                // Also save as order meta for compatibility
                update_post_meta($order_id, '_' . $field_name, $field_value);
            }
        }
    }

    /**
     * Display order fields in admin
     */
    public static function display_order_fields_admin($order) {
        $order_id = $order->get_id();
        $custom_fields = self::get_order_custom_fields($order_id);
        $field_configs = self::get_order_field_configs();
        
        $admin_fields = array_filter($field_configs, function($field) {
            return $field->show_in_admin;
        });
        
        if (empty($admin_fields)) {
            return;
        }
        
        echo '<div class="dab-order-custom-fields">';
        echo '<h3>' . __('Custom Order Fields', 'db-app-builder') . '</h3>';
        
        foreach ($admin_fields as $field_config) {
            $field_name = $field_config->field_name;
            $field_value = isset($custom_fields[$field_name]) ? $custom_fields[$field_name] : '';
            
            if (!empty($field_value)) {
                echo '<p><strong>' . esc_html($field_config->field_label) . ':</strong> ' . esc_html($field_value) . '</p>';
            }
        }
        
        echo '</div>';
    }

    /**
     * Display order fields in emails
     */
    public static function display_order_fields_email($order, $sent_to_admin, $plain_text) {
        $order_id = $order->get_id();
        $custom_fields = self::get_order_custom_fields($order_id);
        $field_configs = self::get_order_field_configs();
        
        $email_fields = array_filter($field_configs, function($field) {
            return $field->show_in_email;
        });
        
        if (empty($email_fields)) {
            return;
        }
        
        if ($plain_text) {
            echo "\n" . __('Additional Order Information:', 'db-app-builder') . "\n";
            echo str_repeat('-', 50) . "\n";
            
            foreach ($email_fields as $field_config) {
                $field_name = $field_config->field_name;
                $field_value = isset($custom_fields[$field_name]) ? $custom_fields[$field_name] : '';
                
                if (!empty($field_value)) {
                    echo $field_config->field_label . ': ' . $field_value . "\n";
                }
            }
        } else {
            echo '<h2>' . __('Additional Order Information', 'db-app-builder') . '</h2>';
            echo '<table cellspacing="0" cellpadding="6" style="width: 100%; border: 1px solid #eee;" border="1" bordercolor="#eee">';
            
            foreach ($email_fields as $field_config) {
                $field_name = $field_config->field_name;
                $field_value = isset($custom_fields[$field_name]) ? $custom_fields[$field_name] : '';
                
                if (!empty($field_value)) {
                    echo '<tr>';
                    echo '<th scope="row" style="text-align:left; border: 1px solid #eee;">' . esc_html($field_config->field_label) . '</th>';
                    echo '<td style="text-align:left; border: 1px solid #eee;">' . esc_html($field_value) . '</td>';
                    echo '</tr>';
                }
            }
            
            echo '</table>';
        }
    }

    /**
     * Get order field configurations
     */
    public static function get_order_field_configs() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_order_fields';
        
        return $wpdb->get_results(
            "SELECT * FROM $table_name ORDER BY field_order ASC, id ASC"
        );
    }

    /**
     * Get custom fields for a specific order
     */
    public static function get_order_custom_fields($order_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_order_field_values';
        
        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT field_name, field_value FROM $table_name WHERE order_id = %d",
                $order_id
            )
        );
        
        $fields = array();
        foreach ($results as $result) {
            $fields[$result->field_name] = $result->field_value;
        }
        
        return $fields;
    }

    /**
     * Save order field value
     */
    public static function save_order_field_value($order_id, $field_name, $field_value) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_wc_order_field_values';
        
        $wpdb->replace(
            $table_name,
            array(
                'order_id' => $order_id,
                'field_name' => $field_name,
                'field_value' => $field_value,
                'updated_at' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%s')
        );
    }
}
