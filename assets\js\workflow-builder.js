/**
 * Workflow Builder JavaScript
 */

jQuery(document).ready(function($) {
    'use strict';

    // Global variables
    let currentWorkflow = null;
    let workflowSteps = [];
    let stepCounter = 0;

    // Initialize workflow builder
    function initWorkflowBuilder() {
        bindEvents();
        initDragAndDrop();
        
        // Load existing workflow if in edit mode
        if (window.dabWorkflowData.isEditMode && window.dabWorkflowData.currentWorkflowId > 0) {
            loadWorkflow(window.dabWorkflowData.currentWorkflowId);
        }
    }

    // Bind event handlers
    function bindEvents() {
        // Create workflow buttons
        $(document).on('click', '#create-workflow-btn, #create-first-workflow-btn', showCreateWorkflowModal);
        
        // Modal events
        $(document).on('click', '#cancel-workflow-creation, .dab-modal-close', hideCreateWorkflowModal);
        $(document).on('click', '#confirm-workflow-creation', createWorkflow);
        
        // Workflow list events
        $(document).on('click', '.edit-workflow-btn', editWorkflow);
        $(document).on('click', '.duplicate-workflow-btn', duplicateWorkflow);
        $(document).on('click', '.delete-workflow-btn', deleteWorkflow);
        
        // Editor events
        $(document).on('click', '#back-to-list-btn', backToList);
        $(document).on('click', '#save-workflow-btn', saveWorkflow);
        $(document).on('click', '#test-workflow-btn', testWorkflow);
        
        // Trigger type change
        $(document).on('change', '#trigger-type', handleTriggerTypeChange);
        
        // Webhook key generation
        $(document).on('click', '#generate-webhook-key', generateWebhookKey);
        
        // Close modal on outside click
        $(document).on('click', '.dab-modal', function(e) {
            if (e.target === this) {
                hideCreateWorkflowModal();
            }
        });
    }

    // Initialize drag and drop functionality
    function initDragAndDrop() {
        // Make action items draggable
        $('.dab-action-item').draggable({
            helper: 'clone',
            revert: 'invalid',
            zIndex: 1000,
            start: function(event, ui) {
                ui.helper.addClass('dab-dragging');
            }
        });

        // Make canvas droppable
        $('#workflow-canvas').droppable({
            accept: '.dab-action-item',
            drop: function(event, ui) {
                const actionType = ui.draggable.data('action');
                const position = {
                    x: event.pageX - $(this).offset().left - 100,
                    y: event.pageY - $(this).offset().top - 50
                };
                addWorkflowStep(actionType, position);
            }
        });
    }

    // Show create workflow modal
    function showCreateWorkflowModal() {
        $('#workflow-creation-modal').fadeIn(300);
        $('#new-workflow-name').focus();
    }

    // Hide create workflow modal
    function hideCreateWorkflowModal() {
        $('#workflow-creation-modal').fadeOut(300);
        $('#new-workflow-name').val('');
        $('#new-workflow-description').val('');
    }

    // Create new workflow
    function createWorkflow() {
        const name = $('#new-workflow-name').val().trim();
        const description = $('#new-workflow-description').val().trim();

        if (!name) {
            alert('Please enter a workflow name.');
            return;
        }

        const data = {
            action: 'dab_save_workflow',
            nonce: window.dabWorkflowData.nonce,
            name: name,
            description: description,
            trigger_type: 'manual',
            trigger_config: {},
            workflow_steps: [],
            is_active: true
        };

        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    hideCreateWorkflowModal();
                    // Redirect to edit mode
                    window.location.href = window.location.href + '&action=edit&workflow_id=' + response.data.workflow_id;
                } else {
                    alert('Error creating workflow: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error creating workflow. Please try again.');
            }
        });
    }

    // Edit workflow
    function editWorkflow() {
        const workflowId = $(this).data('workflow-id');
        window.location.href = window.location.href + '&action=edit&workflow_id=' + workflowId;
    }

    // Duplicate workflow
    function duplicateWorkflow() {
        const workflowId = $(this).data('workflow-id');
        
        if (!confirm('Are you sure you want to duplicate this workflow?')) {
            return;
        }

        // Load workflow data and create a copy
        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_load_workflow',
                workflow_id: workflowId,
                nonce: window.dabWorkflowData.nonce
            },
            success: function(response) {
                if (response.success) {
                    const workflow = response.data;
                    const data = {
                        action: 'dab_save_workflow',
                        nonce: window.dabWorkflowData.nonce,
                        name: workflow.name + ' (Copy)',
                        description: workflow.description,
                        trigger_type: workflow.trigger_type,
                        trigger_config: workflow.trigger_config,
                        workflow_steps: workflow.workflow_steps,
                        is_active: false
                    };

                    $.ajax({
                        url: window.dabWorkflowData.ajaxUrl,
                        type: 'POST',
                        data: data,
                        success: function(response) {
                            if (response.success) {
                                location.reload();
                            } else {
                                alert('Error duplicating workflow: ' + (response.data || 'Unknown error'));
                            }
                        }
                    });
                }
            }
        });
    }

    // Delete workflow
    function deleteWorkflow() {
        const workflowId = $(this).data('workflow-id');
        
        if (!confirm('Are you sure you want to delete this workflow? This action cannot be undone.')) {
            return;
        }

        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_delete_workflow',
                workflow_id: workflowId,
                nonce: window.dabWorkflowData.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error deleting workflow: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error deleting workflow. Please try again.');
            }
        });
    }

    // Back to workflows list
    function backToList() {
        const url = new URL(window.location);
        url.searchParams.delete('action');
        url.searchParams.delete('workflow_id');
        window.location.href = url.toString();
    }

    // Handle trigger type change
    function handleTriggerTypeChange() {
        const triggerType = $(this).val();
        
        // Hide all config sections
        $('.dab-trigger-config-section').hide();
        
        if (triggerType) {
            $('#trigger-config').show();
            $('#' + triggerType.replace('_', '-') + '-config').show();
            
            // Generate webhook key if webhook trigger
            if (triggerType === 'webhook') {
                generateWebhookKey();
            }
        } else {
            $('#trigger-config').hide();
        }
    }

    // Generate webhook key
    function generateWebhookKey() {
        const key = 'wh_' + Math.random().toString(36).substr(2, 16);
        $('#webhook-key').val(key);
        
        const webhookUrl = window.location.origin + '/wp-admin/admin-ajax.php?action=dab_webhook_trigger&key=' + key;
        $('#webhook-url').val(webhookUrl);
    }

    // Add workflow step
    function addWorkflowStep(actionType, position) {
        stepCounter++;
        
        const stepId = 'step-' + stepCounter;
        const actionConfig = getActionConfig(actionType);
        
        const stepHtml = `
            <div class="dab-workflow-step" id="${stepId}" data-action="${actionType}" style="left: ${position.x}px; top: ${position.y}px;">
                <div class="dab-workflow-step-header">
                    <div class="dab-workflow-step-title">
                        <span class="dashicons ${actionConfig.icon}"></span>
                        ${actionConfig.label}
                    </div>
                    <div class="dab-workflow-step-actions">
                        <button type="button" class="dab-workflow-step-btn edit-step-btn" title="Edit">
                            <span class="dashicons dashicons-edit"></span>
                        </button>
                        <button type="button" class="dab-workflow-step-btn delete-step-btn" title="Delete">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>
                <div class="dab-workflow-step-content">
                    ${actionConfig.description}
                </div>
            </div>
        `;
        
        $('#workflow-canvas').append(stepHtml);
        $('.dab-canvas-placeholder').hide();
        
        // Make step draggable
        $('#' + stepId).draggable({
            containment: '#workflow-canvas',
            handle: '.dab-workflow-step-header'
        });
        
        // Add to steps array
        workflowSteps.push({
            id: stepId,
            action: actionType,
            config: {},
            position: position
        });
    }

    // Get action configuration
    function getActionConfig(actionType) {
        const configs = {
            send_email: {
                label: 'Send Email',
                icon: 'dashicons-email-alt',
                description: 'Send an email notification'
            },
            create_record: {
                label: 'Create Record',
                icon: 'dashicons-plus-alt',
                description: 'Create a new record in a table'
            },
            update_record: {
                label: 'Update Record',
                icon: 'dashicons-edit',
                description: 'Update an existing record'
            },
            delete_record: {
                label: 'Delete Record',
                icon: 'dashicons-trash',
                description: 'Delete a record from a table'
            },
            api_call: {
                label: 'API Call',
                icon: 'dashicons-cloud',
                description: 'Make an HTTP API request'
            },
            condition: {
                label: 'Condition',
                icon: 'dashicons-randomize',
                description: 'Branch workflow based on conditions'
            },
            delay: {
                label: 'Delay',
                icon: 'dashicons-clock',
                description: 'Wait for a specified time'
            },
            calculate: {
                label: 'Calculate',
                icon: 'dashicons-calculator',
                description: 'Perform calculations and store results'
            }
        };
        
        return configs[actionType] || {
            label: 'Unknown Action',
            icon: 'dashicons-admin-generic',
            description: 'Unknown action type'
        };
    }

    // Load workflow
    function loadWorkflow(workflowId) {
        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_load_workflow',
                workflow_id: workflowId,
                nonce: window.dabWorkflowData.nonce
            },
            success: function(response) {
                if (response.success) {
                    currentWorkflow = response.data;
                    populateWorkflowForm(currentWorkflow);
                } else {
                    alert('Error loading workflow: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error loading workflow. Please try again.');
            }
        });
    }

    // Populate workflow form
    function populateWorkflowForm(workflow) {
        $('#workflow-name').val(workflow.name);
        $('#workflow-description').val(workflow.description);
        $('#trigger-type').val(workflow.trigger_type).trigger('change');
        $('#workflow-active').prop('checked', workflow.is_active == 1);
        
        // Populate trigger config
        if (workflow.trigger_config) {
            Object.keys(workflow.trigger_config).forEach(key => {
                $('#trigger-' + key.replace('_', '-')).val(workflow.trigger_config[key]);
            });
        }
        
        // Load workflow steps
        if (workflow.workflow_steps && workflow.workflow_steps.length > 0) {
            workflow.workflow_steps.forEach((step, index) => {
                const position = step.position || { x: 50 + (index * 220), y: 50 };
                addWorkflowStep(step.action, position);
            });
        }
    }

    // Save workflow
    function saveWorkflow() {
        const name = $('#workflow-name').val().trim();
        const description = $('#workflow-description').val().trim();
        const triggerType = $('#trigger-type').val();
        const isActive = $('#workflow-active').is(':checked');

        if (!name) {
            alert('Please enter a workflow name.');
            return;
        }

        if (!triggerType) {
            alert('Please select a trigger type.');
            return;
        }

        // Collect trigger config
        const triggerConfig = {};
        $('#trigger-config input, #trigger-config select').each(function() {
            const key = $(this).attr('id').replace('trigger-', '').replace('-', '_');
            triggerConfig[key] = $(this).val();
        });

        const data = {
            action: 'dab_save_workflow',
            nonce: window.dabWorkflowData.nonce,
            workflow_id: window.dabWorkflowData.currentWorkflowId,
            name: name,
            description: description,
            trigger_type: triggerType,
            trigger_config: triggerConfig,
            workflow_steps: workflowSteps,
            is_active: isActive
        };

        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    alert('Workflow saved successfully!');
                } else {
                    alert('Error saving workflow: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error saving workflow. Please try again.');
            }
        });
    }

    // Test workflow
    function testWorkflow() {
        if (!currentWorkflow) {
            alert('Please save the workflow first.');
            return;
        }

        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_test_workflow',
                workflow_id: window.dabWorkflowData.currentWorkflowId,
                nonce: window.dabWorkflowData.nonce
            },
            success: function(response) {
                if (response.success) {
                    alert('Workflow test completed successfully!');
                } else {
                    alert('Workflow test failed: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error testing workflow. Please try again.');
            }
        });
    }

    // Initialize the workflow builder
    initWorkflowBuilder();
});
