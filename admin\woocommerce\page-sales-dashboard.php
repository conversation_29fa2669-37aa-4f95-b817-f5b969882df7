<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo '<div class="notice notice-error"><p>' . __('WooCommerce is required for this feature.', 'db-app-builder') . '</p></div>';
    return;
}

// Get sales data for different periods
$today_data = DAB_Sales_Dashboard_Manager::get_sales_data_for_period('today');
$yesterday_data = DAB_Sales_Dashboard_Manager::get_sales_data_for_period('yesterday');
$this_week_data = DAB_Sales_Dashboard_Manager::get_sales_data_for_period('this_week');
$this_month_data = DAB_Sales_Dashboard_Manager::get_sales_data_for_period('this_month');

// Calculate percentage changes
$sales_change = DAB_Sales_Dashboard_Manager::calculate_percentage_change($today_data['total_sales'], $yesterday_data['total_sales']);
$orders_change = DAB_Sales_Dashboard_Manager::calculate_percentage_change($today_data['total_orders'], $yesterday_data['total_orders']);
$avg_order_change = DAB_Sales_Dashboard_Manager::calculate_percentage_change($today_data['average_order_value'], $yesterday_data['average_order_value']);

// Get top products
global $wpdb;
$top_products = $wpdb->get_results("
    SELECT p.post_title as product_name, 
           SUM(oim.meta_value) as quantity_sold,
           SUM(oim.meta_value * oim2.meta_value) as total_sales
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON p.ID = oim.meta_value
    INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim2 ON oim.order_item_id = oim2.order_item_id
    INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON oim.order_item_id = oi.order_item_id
    INNER JOIN {$wpdb->posts} orders ON oi.order_id = orders.ID
    WHERE oim.meta_key = '_product_id'
    AND oim2.meta_key = '_qty'
    AND orders.post_status IN ('wc-completed', 'wc-processing')
    AND orders.post_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    GROUP BY p.ID
    ORDER BY quantity_sold DESC
    LIMIT 10
");

// Get recent orders
$recent_orders = $wpdb->get_results("
    SELECT p.ID, p.post_date, pm.meta_value as total, pm2.meta_value as status,
           pm3.meta_value as customer_id, u.display_name as customer_name
    FROM {$wpdb->posts} p
    LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_order_total'
    LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_order_status'
    LEFT JOIN {$wpdb->postmeta} pm3 ON p.ID = pm3.post_id AND pm3.meta_key = '_customer_user'
    LEFT JOIN {$wpdb->users} u ON pm3.meta_value = u.ID
    WHERE p.post_type = 'shop_order'
    AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold', 'wc-pending')
    ORDER BY p.post_date DESC
    LIMIT 15
");
?>

<div class="wrap">
    <h1><?php _e('WooCommerce Sales Dashboard', 'db-app-builder'); ?></h1>
    
    <div class="dab-woocommerce-integration-header">
        <p><?php _e('Comprehensive sales analytics and reporting dashboard for your WooCommerce store.', 'db-app-builder'); ?></p>
    </div>

    <!-- Sales Overview Cards -->
    <div class="dab-stats-grid">
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo wc_price($today_data['total_sales']); ?></div>
            <div class="dab-stat-label"><?php _e('Today\'s Sales', 'db-app-builder'); ?></div>
            <div class="dab-stat-change <?php echo $sales_change >= 0 ? 'positive' : 'negative'; ?>">
                <?php echo $sales_change >= 0 ? '+' : ''; ?><?php echo $sales_change; ?>% <?php _e('vs yesterday', 'db-app-builder'); ?>
            </div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($today_data['total_orders']); ?></div>
            <div class="dab-stat-label"><?php _e('Today\'s Orders', 'db-app-builder'); ?></div>
            <div class="dab-stat-change <?php echo $orders_change >= 0 ? 'positive' : 'negative'; ?>">
                <?php echo $orders_change >= 0 ? '+' : ''; ?><?php echo $orders_change; ?>% <?php _e('vs yesterday', 'db-app-builder'); ?>
            </div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo wc_price($today_data['average_order_value']); ?></div>
            <div class="dab-stat-label"><?php _e('Avg Order Value', 'db-app-builder'); ?></div>
            <div class="dab-stat-change <?php echo $avg_order_change >= 0 ? 'positive' : 'negative'; ?>">
                <?php echo $avg_order_change >= 0 ? '+' : ''; ?><?php echo $avg_order_change; ?>% <?php _e('vs yesterday', 'db-app-builder'); ?>
            </div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo wc_price($this_month_data['total_sales']); ?></div>
            <div class="dab-stat-label"><?php _e('This Month\'s Sales', 'db-app-builder'); ?></div>
            <div class="dab-stat-sublabel"><?php echo number_format($this_month_data['total_orders']); ?> <?php _e('orders', 'db-app-builder'); ?></div>
        </div>
    </div>

    <div class="dab-admin-container">
        <div class="dab-admin-main">
            <!-- Sales Chart -->
            <div class="dab-card">
                <div class="dab-card-header">
                    <h2><?php _e('Sales Chart', 'db-app-builder'); ?></h2>
                    <div class="dab-chart-controls">
                        <select id="chart-period">
                            <option value="7days"><?php _e('Last 7 Days', 'db-app-builder'); ?></option>
                            <option value="30days" selected><?php _e('Last 30 Days', 'db-app-builder'); ?></option>
                            <option value="90days"><?php _e('Last 90 Days', 'db-app-builder'); ?></option>
                        </select>
                        <select id="chart-type">
                            <option value="sales"><?php _e('Sales Amount', 'db-app-builder'); ?></option>
                            <option value="orders"><?php _e('Order Count', 'db-app-builder'); ?></option>
                        </select>
                    </div>
                </div>
                <div class="dab-chart-container">
                    <canvas id="sales-chart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="dab-card">
                <h2><?php _e('Recent Orders', 'db-app-builder'); ?></h2>
                
                <?php if (empty($recent_orders)): ?>
                    <p><?php _e('No recent orders found.', 'db-app-builder'); ?></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Order', 'db-app-builder'); ?></th>
                                <th><?php _e('Date', 'db-app-builder'); ?></th>
                                <th><?php _e('Customer', 'db-app-builder'); ?></th>
                                <th><?php _e('Status', 'db-app-builder'); ?></th>
                                <th><?php _e('Total', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_orders as $order): ?>
                                <tr>
                                    <td>
                                        <strong><a href="<?php echo admin_url('post.php?post=' . $order->ID . '&action=edit'); ?>">#<?php echo $order->ID; ?></a></strong>
                                    </td>
                                    <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($order->post_date)); ?></td>
                                    <td>
                                        <?php if ($order->customer_name): ?>
                                            <?php echo esc_html($order->customer_name); ?>
                                        <?php else: ?>
                                            <em><?php _e('Guest', 'db-app-builder'); ?></em>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status = $order->status ?: 'pending';
                                        $status_name = wc_get_order_status_name($status);
                                        echo '<span class="order-status status-' . esc_attr($status) . '">' . esc_html($status_name) . '</span>';
                                        ?>
                                    </td>
                                    <td><?php echo wc_price($order->total); ?></td>
                                    <td>
                                        <a href="<?php echo admin_url('post.php?post=' . $order->ID . '&action=edit'); ?>" class="button button-small"><?php _e('View', 'db-app-builder'); ?></a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <div class="dab-admin-sidebar">
            <!-- Top Products -->
            <div class="dab-card">
                <h3><?php _e('Top Products (30 Days)', 'db-app-builder'); ?></h3>
                
                <?php if (empty($top_products)): ?>
                    <p><?php _e('No product sales data available.', 'db-app-builder'); ?></p>
                <?php else: ?>
                    <div class="dab-top-products">
                        <?php foreach ($top_products as $index => $product): ?>
                            <div class="dab-product-item">
                                <div class="dab-product-rank"><?php echo $index + 1; ?></div>
                                <div class="dab-product-info">
                                    <div class="dab-product-name"><?php echo esc_html($product->product_name); ?></div>
                                    <div class="dab-product-stats">
                                        <?php echo number_format($product->quantity_sold); ?> <?php _e('sold', 'db-app-builder'); ?> • 
                                        <?php echo wc_price($product->total_sales); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Quick Stats -->
            <div class="dab-card">
                <h3><?php _e('Quick Stats', 'db-app-builder'); ?></h3>
                <div class="dab-quick-stats">
                    <div class="dab-quick-stat">
                        <div class="dab-quick-stat-label"><?php _e('This Week', 'db-app-builder'); ?></div>
                        <div class="dab-quick-stat-value"><?php echo wc_price($this_week_data['total_sales']); ?></div>
                        <div class="dab-quick-stat-sub"><?php echo number_format($this_week_data['total_orders']); ?> <?php _e('orders', 'db-app-builder'); ?></div>
                    </div>
                    
                    <div class="dab-quick-stat">
                        <div class="dab-quick-stat-label"><?php _e('This Month', 'db-app-builder'); ?></div>
                        <div class="dab-quick-stat-value"><?php echo wc_price($this_month_data['total_sales']); ?></div>
                        <div class="dab-quick-stat-sub"><?php echo number_format($this_month_data['total_orders']); ?> <?php _e('orders', 'db-app-builder'); ?></div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="dab-card">
                <h3><?php _e('Quick Actions', 'db-app-builder'); ?></h3>
                <ul class="dab-quick-actions">
                    <li><a href="<?php echo admin_url('edit.php?post_type=shop_order'); ?>"><?php _e('View All Orders', 'db-app-builder'); ?></a></li>
                    <li><a href="<?php echo admin_url('edit.php?post_type=product'); ?>"><?php _e('Manage Products', 'db-app-builder'); ?></a></li>
                    <li><a href="<?php echo admin_url('admin.php?page=wc-reports'); ?>"><?php _e('WooCommerce Reports', 'db-app-builder'); ?></a></li>
                    <li><a href="<?php echo admin_url('users.php?role=customer'); ?>"><?php _e('View Customers', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="exportSalesReport()"><?php _e('Export Sales Report', 'db-app-builder'); ?></a></li>
                </ul>
            </div>

            <!-- Dashboard Settings -->
            <div class="dab-card">
                <h3><?php _e('Dashboard Settings', 'db-app-builder'); ?></h3>
                <div class="dab-dashboard-settings">
                    <label>
                        <input type="checkbox" id="auto-refresh" checked>
                        <?php _e('Auto-refresh data', 'db-app-builder'); ?>
                    </label>
                    <br>
                    <label>
                        <?php _e('Refresh interval:', 'db-app-builder'); ?>
                        <select id="refresh-interval">
                            <option value="60">1 <?php _e('minute', 'db-app-builder'); ?></option>
                            <option value="300" selected>5 <?php _e('minutes', 'db-app-builder'); ?></option>
                            <option value="600">10 <?php _e('minutes', 'db-app-builder'); ?></option>
                        </select>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize chart
let salesChart;

jQuery(document).ready(function($) {
    // Initialize the sales chart
    initSalesChart();
    
    // Chart controls
    $('#chart-period, #chart-type').change(function() {
        updateSalesChart();
    });
    
    // Auto-refresh functionality
    let refreshInterval;
    
    function setupAutoRefresh() {
        if ($('#auto-refresh').is(':checked')) {
            const interval = parseInt($('#refresh-interval').val()) * 1000;
            refreshInterval = setInterval(function() {
                updateSalesChart();
                updateStats();
            }, interval);
        } else {
            clearInterval(refreshInterval);
        }
    }
    
    $('#auto-refresh, #refresh-interval').change(setupAutoRefresh);
    setupAutoRefresh();
});

function initSalesChart() {
    const ctx = document.getElementById('sales-chart').getContext('2d');
    
    salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '<?php _e('Sales', 'db-app-builder'); ?>',
                data: [],
                borderColor: '#2271b1',
                backgroundColor: 'rgba(34, 113, 177, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    updateSalesChart();
}

function updateSalesChart() {
    const period = jQuery('#chart-period').val();
    const type = jQuery('#chart-type').val();
    
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'dab_get_sales_chart_data',
            period: period,
            type: type,
            nonce: '<?php echo wp_create_nonce('dab_sales_dashboard_nonce'); ?>'
        },
        success: function(response) {
            if (response.success) {
                salesChart.data.labels = response.data.labels;
                salesChart.data.datasets[0].data = response.data.values;
                salesChart.data.datasets[0].label = response.data.label;
                salesChart.update();
            }
        }
    });
}

function updateStats() {
    // Update dashboard stats via AJAX
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'dab_get_sales_data',
            nonce: '<?php echo wp_create_nonce('dab_sales_dashboard_nonce'); ?>'
        },
        success: function(response) {
            if (response.success) {
                // Update stat cards with new data
                // Implementation would update the displayed values
            }
        }
    });
}

function exportSalesReport() {
    // Export sales report functionality
    alert('<?php _e('Sales report export will be implemented in the next update.', 'db-app-builder'); ?>');
}
</script>

<style>
.dab-woocommerce-integration-header {
    background: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #2271b1;
    padding: 15px;
    margin: 20px 0;
}

.dab-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dab-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.dab-stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #2271b1;
    line-height: 1;
}

.dab-stat-label {
    font-size: 14px;
    color: #666;
    margin: 5px 0;
}

.dab-stat-sublabel {
    font-size: 12px;
    color: #999;
}

.dab-stat-change {
    font-size: 12px;
    margin-top: 5px;
}

.dab-stat-change.positive {
    color: #00a32a;
}

.dab-stat-change.negative {
    color: #d63638;
}

.dab-admin-container {
    display: flex;
    gap: 20px;
}

.dab-admin-main {
    flex: 2;
}

.dab-admin-sidebar {
    flex: 1;
}

.dab-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.dab-card h2, .dab-card h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.dab-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.dab-card-header h2 {
    margin: 0;
    padding: 0;
    border: none;
}

.dab-chart-controls {
    display: flex;
    gap: 10px;
}

.dab-chart-container {
    height: 300px;
    position: relative;
}

.dab-top-products {
    max-height: 400px;
    overflow-y: auto;
}

.dab-product-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.dab-product-rank {
    width: 30px;
    height: 30px;
    background: #2271b1;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
}

.dab-product-info {
    flex: 1;
}

.dab-product-name {
    font-weight: bold;
    margin-bottom: 3px;
}

.dab-product-stats {
    font-size: 12px;
    color: #666;
}

.dab-quick-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dab-quick-stat {
    text-align: center;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.dab-quick-stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.dab-quick-stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #2271b1;
}

.dab-quick-stat-sub {
    font-size: 11px;
    color: #999;
    margin-top: 3px;
}

.dab-quick-actions {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.dab-quick-actions li {
    padding: 5px 0;
}

.dab-quick-actions a {
    text-decoration: none;
}

.dab-dashboard-settings label {
    display: block;
    margin-bottom: 10px;
}

.order-status {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.order-status.status-completed {
    background: #c6e1c6;
    color: #5b841b;
}

.order-status.status-processing {
    background: #c8d7e1;
    color: #2e4453;
}

.order-status.status-on-hold {
    background: #f8dda7;
    color: #94660c;
}

.order-status.status-pending {
    background: #e5e5e5;
    color: #999;
}
</style>
