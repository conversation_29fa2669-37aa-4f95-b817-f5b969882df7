<?php
/**
 * Field Types Manager
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/admin
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DAB_Field_Types {
    /**
     * Returns an array of available field types
     */
    public function get_field_types() {
        $field_types = array(
            'text' => __('Text', 'db-app-builder'),
            'textarea' => __('Textarea', 'db-app-builder'),
            'number' => __('Number', 'db-app-builder'),
            'email' => __('Email', 'db-app-builder'),
            'url' => __('URL', 'db-app-builder'),
            'date' => __('Date', 'db-app-builder'),
            'time' => __('Time', 'db-app-builder'),
            'datetime' => __('Date & Time', 'db-app-builder'),
            'select' => __('Select', 'db-app-builder'),
            'radio' => __('Radio', 'db-app-builder'),
            'checkbox' => __('Checkbox', 'db-app-builder'),
            'file' => __('File Upload', 'db-app-builder'),
            'image' => __('Image Upload', 'db-app-builder'),
            'lookup' => __('Lookup', 'db-app-builder'),
            'formula' => __('Formula', 'db-app-builder'),
            'inline_table' => __('Inline Table', 'db-app-builder'),
            'autoincrement' => __('Auto Increment', 'db-app-builder'),
            'signature' => __('Signature Capture', 'db-app-builder'),
            'media' => __('Audio/Video Media', 'db-app-builder'),
            'conditional_logic' => __('Conditional Logic', 'db-app-builder'),
            'social_media' => __('Social Media Links', 'db-app-builder'),
            'multiselect' => __('Multi-select Dropdown', 'db-app-builder'),
            'enhanced_currency' => __('Currency (Enhanced)', 'db-app-builder'),
        );

        return apply_filters('dab_field_types', $field_types);
    }

    /**
     * Get field type options for specific field types
     */
    public function get_field_type_options($field_type) {
        // Returns specific options based on field type
        switch ($field_type) {
            case 'select':
            case 'radio':
            case 'checkbox':
            case 'multiselect':
                return $this->get_choice_field_options();
            case 'lookup':
                return $this->get_lookup_field_options();
            case 'inline_table':
                return $this->get_inline_table_options();
            case 'autoincrement':
                return $this->get_autoincrement_field_options();
            case 'signature':
                // Signature field options are handled by the DAB_Signature_Field class
                return apply_filters('dab_field_type_options_signature', array());
            case 'media':
                // Media field options are handled by the DAB_Media_Field class
                return apply_filters('dab_field_type_options_media', array());
            case 'conditional_logic':
                // Conditional logic field options are handled by the DAB_Conditional_Logic_Field class
                return apply_filters('dab_field_type_options_conditional_logic', array());
            case 'social_media':
                // Social media field options are handled by the DAB_Social_Media_Field class
                return apply_filters('dab_field_type_options_social_media', array());
            case 'enhanced_currency':
                // Enhanced currency field options are handled by the DAB_Enhanced_Currency_Field class
                return apply_filters('dab_field_type_options_enhanced_currency', array());
            // Other field type options...
        }

        return array();
    }

    /**
     * Get options for autoincrement field
     */
    public function get_autoincrement_field_options() {
        return array(
            'prefix' => array(
                'label' => __('Prefix', 'db-app-builder'),
                'type' => 'text',
                'description' => __('Text to prepend to the number (e.g., "STU" for "STU001")', 'db-app-builder'),
            ),
            'start_number' => array(
                'label' => __('Starting Number', 'db-app-builder'),
                'type' => 'number',
                'description' => __('The number to start from (default: 1)', 'db-app-builder'),
                'default' => 1,
            ),
            'padding' => array(
                'label' => __('Zero Padding', 'db-app-builder'),
                'type' => 'number',
                'description' => __('Number of digits to pad with zeros (e.g., 3 for "001")', 'db-app-builder'),
                'default' => 3,
            ),
        );
    }

    /**
     * Get options for inline table field
     */
    public function get_inline_table_options() {
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';

        // Get all tables
        $tables = $wpdb->get_results("SELECT id, table_name FROM $tables_table ORDER BY table_name");

        $options = array();
        foreach ($tables as $table) {
            $options[$table->id] = $table->table_name;
        }

        return $options;
    }

    /**
     * Get database column type for a field type
     */
    public function get_db_column_type($field_type) {
        // Maps field types to database column types
        switch ($field_type) {
            case 'text':
            case 'email':
            case 'url':
            case 'autoincrement':
                return 'VARCHAR(255)';
            case 'textarea':
                return 'TEXT';
            case 'number':
                return 'DECIMAL(10,2)';
            case 'date':
                return 'DATE';
            case 'inline_table':
            case 'signature':
            case 'media':
            case 'conditional_logic':
            case 'social_media':
            case 'multiselect':
            case 'enhanced_currency':
                return 'LONGTEXT'; // Store JSON data
            // Other mappings...
        }

        return 'VARCHAR(255)';
    }

    // Other helper methods for field validation, rendering, etc.
}

