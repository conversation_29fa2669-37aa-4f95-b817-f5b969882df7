<?php
/**
 * Social Media Links Field Handler
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DAB_Social_Media_Field {
    /**
     * Initialize the class
     */
    public function __construct() {
        // Register the social media field type
        add_filter('dab_field_types', array($this, 'register_social_media_field_type'));
        
        // Add field options
        add_filter('dab_field_type_options', array($this, 'add_social_media_field_options'), 10, 2);
        
        // Register field renderer
        add_action('dab_render_field_social_media', array($this, 'render_social_media_field'), 10, 2);
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Format social media value for display
        add_filter('dab_format_field_value', array($this, 'format_social_media_value'), 10, 3);
    }
    
    /**
     * Register the social media field type
     * 
     * @param array $field_types Existing field types
     * @return array Modified field types
     */
    public function register_social_media_field_type($field_types) {
        $field_types['social_media'] = __('Social Media Links', 'db-app-builder');
        return $field_types;
    }
    
    /**
     * Add social media field options
     * 
     * @param array $options Existing options
     * @param string $field_type Field type
     * @return array Modified options
     */
    public function add_social_media_field_options($options, $field_type) {
        if ($field_type === 'social_media') {
            $options = array(
                'platforms' => array(
                    'label' => __('Available Platforms', 'db-app-builder'),
                    'type' => 'multiselect',
                    'options' => $this->get_available_platforms(),
                    'description' => __('Select which social media platforms to include.', 'db-app-builder'),
                ),
                'allow_custom' => array(
                    'label' => __('Allow Custom Platforms', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Allow users to add custom platform names.', 'db-app-builder'),
                ),
                'max_links' => array(
                    'label' => __('Maximum Links', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 0,
                    'description' => __('Maximum number of social media links allowed (0 for unlimited).', 'db-app-builder'),
                ),
                'show_icons' => array(
                    'label' => __('Show Icons', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Display platform icons next to inputs.', 'db-app-builder'),
                ),
                'validate_urls' => array(
                    'label' => __('Validate URLs', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Validate URLs according to platform-specific patterns.', 'db-app-builder'),
                ),
            );
        }
        
        return $options;
    }
    
    /**
     * Render the social media field
     * 
     * @param object $field Field object
     * @param mixed $value Field value
     */
    public function render_social_media_field($field, $value) {
        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }
        
        // Get field settings with defaults
        $selected_platforms = isset($options['platforms']) ? (array)$options['platforms'] : array_keys($this->get_available_platforms());
        $allow_custom = isset($options['allow_custom']) ? (bool)$options['allow_custom'] : false;
        $max_links = isset($options['max_links']) ? intval($options['max_links']) : 0;
        $show_icons = isset($options['show_icons']) ? (bool)$options['show_icons'] : true;
        $validate_urls = isset($options['validate_urls']) ? (bool)$options['validate_urls'] : true;
        
        // Get all available platforms
        $available_platforms = $this->get_available_platforms();
        
        // Filter platforms based on selected ones
        $platforms = array();
        foreach ($selected_platforms as $platform_key) {
            if (isset($available_platforms[$platform_key])) {
                $platforms[$platform_key] = $available_platforms[$platform_key];
            }
        }
        
        // Parse the value (stored as JSON)
        $social_links = array();
        if (!empty($value)) {
            if (is_string($value) && strpos($value, '{') === 0) {
                $parsed_value = json_decode($value, true);
                if (is_array($parsed_value)) {
                    $social_links = $parsed_value;
                }
            }
        }
        
        // Generate a unique ID for the field
        $field_id = 'dab-social-media-' . $field->id . '-' . uniqid();
        
        // Output the field HTML
        ?>
        <div class="dab-social-media-field" id="<?php echo esc_attr($field_id); ?>" data-field-slug="<?php echo esc_attr($field->field_slug); ?>">
            <div class="dab-social-media-links">
                <?php 
                // Display existing links
                if (!empty($social_links)) {
                    foreach ($social_links as $index => $link) {
                        $platform = isset($link['platform']) ? $link['platform'] : '';
                        $url = isset($link['url']) ? $link['url'] : '';
                        $is_custom = isset($link['custom']) ? (bool)$link['custom'] : false;
                        
                        $this->render_social_link_row($field_id, $index, $platform, $url, $is_custom, $platforms, $show_icons, $validate_urls);
                    }
                } else {
                    // Display one empty row by default
                    $this->render_social_link_row($field_id, 0, '', '', false, $platforms, $show_icons, $validate_urls);
                }
                ?>
            </div>
            
            <?php if ($max_links === 0 || count($social_links) < $max_links): ?>
            <div class="dab-social-media-actions">
                <button type="button" class="button dab-add-social-link">
                    <span class="dashicons dashicons-plus-alt"></span> <?php _e('Add Social Link', 'db-app-builder'); ?>
                </button>
            </div>
            <?php endif; ?>
            
            <!-- Hidden field to store the combined JSON value -->
            <input type="hidden" name="<?php echo esc_attr($field->field_slug); ?>" id="<?php echo esc_attr($field_id . '-value'); ?>" value="<?php echo esc_attr($value); ?>" <?php echo $field->required ? 'required' : ''; ?>>
            
            <!-- Template for new social link rows -->
            <script type="text/template" id="<?php echo esc_attr($field_id . '-template'); ?>">
                <?php $this->render_social_link_row($field_id, '{{index}}', '', '', false, $platforms, $show_icons, $validate_urls); ?>
            </script>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            var $field = $('#<?php echo esc_js($field_id); ?>');
            var $links = $field.find('.dab-social-media-links');
            var $value = $('#<?php echo esc_js($field_id . '-value'); ?>');
            var $template = $('#<?php echo esc_js($field_id . '-template'); ?>');
            var maxLinks = <?php echo esc_js($max_links); ?>;
            var allowCustom = <?php echo $allow_custom ? 'true' : 'false'; ?>;
            var validateUrls = <?php echo $validate_urls ? 'true' : 'false'; ?>;
            var platformPatterns = <?php echo json_encode($this->get_platform_patterns()); ?>;
            
            // Initialize
            updateValue();
            toggleAddButton();
            
            // Add new social link
            $field.on('click', '.dab-add-social-link', function() {
                var index = $links.children().length;
                var newRow = $template.html().replace(/\{\{index\}\}/g, index);
                $links.append(newRow);
                toggleAddButton();
            });
            
            // Remove social link
            $field.on('click', '.dab-remove-social-link', function() {
                $(this).closest('.dab-social-link-row').remove();
                // Renumber indices
                $links.children().each(function(i) {
                    $(this).find('select, input').each(function() {
                        var name = $(this).attr('name');
                        if (name) {
                            $(this).attr('name', name.replace(/\[\d+\]/, '[' + i + ']'));
                        }
                    });
                });
                updateValue();
                toggleAddButton();
            });
            
            // Toggle custom platform input
            $field.on('change', '.dab-social-platform', function() {
                var $row = $(this).closest('.dab-social-link-row');
                var platform = $(this).val();
                
                if (platform === 'custom' && allowCustom) {
                    $row.find('.dab-custom-platform').show();
                } else {
                    $row.find('.dab-custom-platform').hide();
                }
                
                // Update icon
                if (platform !== 'custom') {
                    var iconClass = 'fab fa-' + platform.toLowerCase();
                    $row.find('.dab-social-icon i').attr('class', iconClass);
                } else {
                    $row.find('.dab-social-icon i').attr('class', 'fas fa-link');
                }
                
                updateValue();
            });
            
            // Update value when inputs change
            $field.on('input change', 'input, select', function() {
                updateValue();
            });
            
            // Validate URL based on platform
            $field.on('blur', '.dab-social-url', function() {
                if (!validateUrls) return;
                
                var $row = $(this).closest('.dab-social-link-row');
                var platform = $row.find('.dab-social-platform').val();
                var url = $(this).val().trim();
                
                if (url && platform !== 'custom' && platformPatterns[platform]) {
                    var pattern = new RegExp(platformPatterns[platform]);
                    if (!pattern.test(url)) {
                        $(this).addClass('dab-invalid-url');
                        // Try to fix common issues
                        if (platform === 'facebook' && url.indexOf('facebook.com') !== -1) {
                            $(this).val('https://www.facebook.com/' + url.split('facebook.com/').pop());
                        } else if (platform === 'twitter' && url.indexOf('twitter.com') !== -1) {
                            $(this).val('https://twitter.com/' + url.split('twitter.com/').pop());
                        } else if (platform === 'instagram' && url.indexOf('instagram.com') !== -1) {
                            $(this).val('https://www.instagram.com/' + url.split('instagram.com/').pop());
                        }
                        updateValue();
                    } else {
                        $(this).removeClass('dab-invalid-url');
                    }
                } else {
                    $(this).removeClass('dab-invalid-url');
                }
            });
            
            // Update the hidden value field
            function updateValue() {
                var links = [];
                
                $links.children().each(function() {
                    var $row = $(this);
                    var platform = $row.find('.dab-social-platform').val();
                    var url = $row.find('.dab-social-url').val().trim();
                    var customPlatform = $row.find('.dab-custom-platform').val().trim();
                    
                    // Skip empty rows
                    if (!platform || !url) return;
                    
                    var link = {
                        platform: platform,
                        url: url
                    };
                    
                    if (platform === 'custom' && customPlatform) {
                        link.custom = true;
                        link.custom_name = customPlatform;
                    }
                    
                    links.push(link);
                });
                
                $value.val(JSON.stringify(links));
            }
            
            // Toggle add button based on max links
            function toggleAddButton() {
                if (maxLinks > 0 && $links.children().length >= maxLinks) {
                    $field.find('.dab-add-social-link').hide();
                } else {
                    $field.find('.dab-add-social-link').show();
                }
            }
        });
        </script>
        <?php
    }
    
    /**
     * Render a social link row
     * 
     * @param string $field_id Field ID
     * @param int $index Row index
     * @param string $platform Selected platform
     * @param string $url URL value
     * @param bool $is_custom Whether this is a custom platform
     * @param array $platforms Available platforms
     * @param bool $show_icons Whether to show icons
     * @param bool $validate_urls Whether to validate URLs
     */
    private function render_social_link_row($field_id, $index, $platform, $url, $is_custom, $platforms, $show_icons, $validate_urls) {
        $custom_platform = '';
        if ($is_custom && $platform) {
            $custom_platform = $platform;
            $platform = 'custom';
        }
        
        $icon_class = 'fab fa-' . strtolower($platform);
        if ($platform === 'custom' || empty($platform)) {
            $icon_class = 'fas fa-link';
        }
        ?>
        <div class="dab-social-link-row">
            <?php if ($show_icons): ?>
            <div class="dab-social-icon">
                <i class="<?php echo esc_attr($icon_class); ?>"></i>
            </div>
            <?php endif; ?>
            
            <div class="dab-social-platform-wrapper">
                <select name="<?php echo esc_attr($field_id . '[' . $index . '][platform]'); ?>" class="dab-social-platform">
                    <option value=""><?php _e('-- Select Platform --', 'db-app-builder'); ?></option>
                    <?php foreach ($platforms as $key => $label): ?>
                        <option value="<?php echo esc_attr($key); ?>" <?php selected($platform, $key); ?>>
                            <?php echo esc_html($label); ?>
                        </option>
                    <?php endforeach; ?>
                    <option value="custom" <?php selected($platform, 'custom'); ?>><?php _e('Custom', 'db-app-builder'); ?></option>
                </select>
                
                <input type="text" 
                       name="<?php echo esc_attr($field_id . '[' . $index . '][custom_platform]'); ?>" 
                       class="dab-custom-platform" 
                       placeholder="<?php esc_attr_e('Custom Platform Name', 'db-app-builder'); ?>" 
                       value="<?php echo esc_attr($custom_platform); ?>" 
                       style="<?php echo $platform === 'custom' ? '' : 'display: none;'; ?>">
            </div>
            
            <div class="dab-social-url-wrapper">
                <input type="url" 
                       name="<?php echo esc_attr($field_id . '[' . $index . '][url]'); ?>" 
                       class="dab-social-url" 
                       placeholder="<?php esc_attr_e('https://', 'db-app-builder'); ?>" 
                       value="<?php echo esc_attr($url); ?>">
            </div>
            
            <div class="dab-social-actions">
                <button type="button" class="button dab-remove-social-link">
                    <span class="dashicons dashicons-trash"></span>
                </button>
            </div>
        </div>
        <?php
    }
    
    /**
     * Format social media value for display
     * 
     * @param mixed $value Field value
     * @param object $field Field object
     * @param string $context Display context
     * @return string Formatted value
     */
    public function format_social_media_value($value, $field, $context) {
        if ($field->field_type !== 'social_media') {
            return $value;
        }
        
        // Parse the value
        $social_links = array();
        if (!empty($value)) {
            if (is_string($value) && strpos($value, '{') === 0) {
                $parsed_value = json_decode($value, true);
                if (is_array($parsed_value)) {
                    $social_links = $parsed_value;
                }
            }
        }
        
        if (empty($social_links)) {
            return '';
        }
        
        // Get available platforms
        $available_platforms = $this->get_available_platforms();
        
        // Format the links
        $formatted_links = array();
        foreach ($social_links as $link) {
            $platform = isset($link['platform']) ? $link['platform'] : '';
            $url = isset($link['url']) ? $link['url'] : '';
            $is_custom = isset($link['custom']) ? (bool)$link['custom'] : false;
            $custom_name = isset($link['custom_name']) ? $link['custom_name'] : '';
            
            if (empty($platform) || empty($url)) {
                continue;
            }
            
            $platform_name = $is_custom ? $custom_name : (isset($available_platforms[$platform]) ? $available_platforms[$platform] : $platform);
            
            if ($context === 'html') {
                $icon_class = 'fab fa-' . strtolower($platform);
                if ($platform === 'custom') {
                    $icon_class = 'fas fa-link';
                }
                
                $formatted_links[] = sprintf(
                    '<a href="%s" target="_blank" rel="noopener noreferrer" class="dab-social-link dab-social-link-%s"><i class="%s"></i> %s</a>',
                    esc_url($url),
                    esc_attr($platform),
                    esc_attr($icon_class),
                    esc_html($platform_name)
                );
            } else {
                $formatted_links[] = sprintf('%s: %s', $platform_name, $url);
            }
        }
        
        // Join the formatted links
        if ($context === 'html') {
            return '<div class="dab-social-links-display">' . implode(' ', $formatted_links) . '</div>';
        } else {
            return implode(', ', $formatted_links);
        }
    }
    
    /**
     * Get available social media platforms
     * 
     * @return array Available platforms
     */
    public function get_available_platforms() {
        return array(
            'facebook' => __('Facebook', 'db-app-builder'),
            'twitter' => __('Twitter', 'db-app-builder'),
            'instagram' => __('Instagram', 'db-app-builder'),
            'linkedin' => __('LinkedIn', 'db-app-builder'),
            'youtube' => __('YouTube', 'db-app-builder'),
            'pinterest' => __('Pinterest', 'db-app-builder'),
            'tiktok' => __('TikTok', 'db-app-builder'),
            'snapchat' => __('Snapchat', 'db-app-builder'),
            'reddit' => __('Reddit', 'db-app-builder'),
            'tumblr' => __('Tumblr', 'db-app-builder'),
            'whatsapp' => __('WhatsApp', 'db-app-builder'),
            'telegram' => __('Telegram', 'db-app-builder'),
            'github' => __('GitHub', 'db-app-builder'),
            'dribbble' => __('Dribbble', 'db-app-builder'),
            'behance' => __('Behance', 'db-app-builder'),
            'medium' => __('Medium', 'db-app-builder'),
            'vimeo' => __('Vimeo', 'db-app-builder'),
            'soundcloud' => __('SoundCloud', 'db-app-builder'),
            'spotify' => __('Spotify', 'db-app-builder'),
            'twitch' => __('Twitch', 'db-app-builder'),
        );
    }
    
    /**
     * Get URL validation patterns for platforms
     * 
     * @return array Platform patterns
     */
    public function get_platform_patterns() {
        return array(
            'facebook' => '^https?://(www\\.)?facebook\\.com/.+',
            'twitter' => '^https?://(www\\.)?twitter\\.com/.+',
            'instagram' => '^https?://(www\\.)?instagram\\.com/.+',
            'linkedin' => '^https?://(www\\.)?linkedin\\.com/.+',
            'youtube' => '^https?://(www\\.)?youtube\\.com/.+',
            'pinterest' => '^https?://(www\\.)?pinterest\\.com/.+',
            'tiktok' => '^https?://(www\\.)?tiktok\\.com/.+',
            'reddit' => '^https?://(www\\.)?reddit\\.com/.+',
            'tumblr' => '^https?://(www\\.)?.+\\.tumblr\\.com/?.*',
            'github' => '^https?://(www\\.)?github\\.com/.+',
            'dribbble' => '^https?://(www\\.)?dribbble\\.com/.+',
            'behance' => '^https?://(www\\.)?behance\\.net/.+',
            'medium' => '^https?://(www\\.)?medium\\.com/.+',
            'vimeo' => '^https?://(www\\.)?vimeo\\.com/.+',
            'soundcloud' => '^https?://(www\\.)?soundcloud\\.com/.+',
            'spotify' => '^https?://(open\\.)?spotify\\.com/.+',
            'twitch' => '^https?://(www\\.)?twitch\\.tv/.+',
        );
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue Font Awesome
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
            array(),
            '5.15.4'
        );
        
        // Enqueue custom styles
        wp_enqueue_style(
            'dab-social-media-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/social-media-field.css',
            array('font-awesome'),
            DAB_VERSION
        );
        
        // Enqueue custom scripts
        wp_enqueue_script(
            'dab-social-media-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/social-media-field.js',
            array('jquery'),
            DAB_VERSION,
            true
        );
    }
}

// Initialize the class
new DAB_Social_Media_Field();
