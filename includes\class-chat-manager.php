<?php
/**
 * Chat Manager
 *
 * Handles chat functionality, messages, and real-time communication
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Chat_Manager {

    /**
     * Initialize the class
     */
    public static function init() {
        // Create tables if they don't exist
        self::create_tables();

        // Register AJAX handlers
        add_action('wp_ajax_dab_send_message', array(__CLASS__, 'ajax_send_message'));
        add_action('wp_ajax_nopriv_dab_send_message', array(__CLASS__, 'ajax_send_message'));

        add_action('wp_ajax_dab_get_messages', array(__CLASS__, 'ajax_get_messages'));
        add_action('wp_ajax_nopriv_dab_get_messages', array(__CLASS__, 'ajax_get_messages'));

        add_action('wp_ajax_dab_get_conversations', array(__CLASS__, 'ajax_get_conversations'));
        add_action('wp_ajax_nopriv_dab_get_conversations', array(__CLASS__, 'ajax_get_conversations'));

        add_action('wp_ajax_dab_mark_messages_read', array(__CLASS__, 'ajax_mark_messages_read'));
        add_action('wp_ajax_nopriv_dab_mark_messages_read', array(__CLASS__, 'ajax_mark_messages_read'));

        add_action('wp_ajax_dab_delete_message', array(__CLASS__, 'ajax_delete_message'));
        add_action('wp_ajax_nopriv_dab_delete_message', array(__CLASS__, 'ajax_delete_message'));

        add_action('wp_ajax_dab_search_users', array(__CLASS__, 'ajax_search_users'));
        add_action('wp_ajax_nopriv_dab_search_users', array(__CLASS__, 'ajax_search_users'));

        add_action('wp_ajax_dab_get_user_status', array(__CLASS__, 'ajax_get_user_status'));
        add_action('wp_ajax_nopriv_dab_get_user_status', array(__CLASS__, 'ajax_get_user_status'));

        add_action('wp_ajax_dab_update_user_status', array(__CLASS__, 'ajax_update_user_status'));
        add_action('wp_ajax_nopriv_dab_update_user_status', array(__CLASS__, 'ajax_update_user_status'));

        // Update user last seen on activity
        add_action('wp_ajax_dab_update_last_seen', array(__CLASS__, 'ajax_update_last_seen'));
        add_action('wp_ajax_nopriv_dab_update_last_seen', array(__CLASS__, 'ajax_update_last_seen'));
    }

    /**
     * Create necessary tables for chat system
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Messages table
        $messages_table = $wpdb->prefix . 'dab_chat_messages';
        $sql_messages = "CREATE TABLE IF NOT EXISTS $messages_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            sender_id BIGINT(20) UNSIGNED NOT NULL,
            recipient_id BIGINT(20) UNSIGNED NULL,
            group_id BIGINT(20) UNSIGNED NULL,
            message TEXT NOT NULL,
            message_type VARCHAR(20) DEFAULT 'text',
            attachment_url VARCHAR(500) NULL,
            attachment_name VARCHAR(255) NULL,
            attachment_size INT NULL,
            is_read TINYINT(1) DEFAULT 0,
            is_deleted TINYINT(1) DEFAULT 0,
            reply_to_id BIGINT(20) UNSIGNED NULL,
            edited_at DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY sender_id (sender_id),
            KEY recipient_id (recipient_id),
            KEY group_id (group_id),
            KEY created_at (created_at),
            KEY is_read (is_read),
            KEY is_deleted (is_deleted)
        ) $charset_collate;";

        // Conversations table
        $conversations_table = $wpdb->prefix . 'dab_chat_conversations';
        $sql_conversations = "CREATE TABLE IF NOT EXISTS $conversations_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user1_id BIGINT(20) UNSIGNED NOT NULL,
            user2_id BIGINT(20) UNSIGNED NOT NULL,
            last_message_id BIGINT(20) UNSIGNED NULL,
            last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
            user1_deleted TINYINT(1) DEFAULT 0,
            user2_deleted TINYINT(1) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_conversation (user1_id, user2_id),
            KEY last_activity (last_activity)
        ) $charset_collate;";

        // User status table
        $user_status_table = $wpdb->prefix . 'dab_chat_user_status';
        $sql_user_status = "CREATE TABLE IF NOT EXISTS $user_status_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) UNSIGNED NOT NULL UNIQUE,
            status VARCHAR(20) DEFAULT 'offline',
            status_message VARCHAR(255) NULL,
            last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_typing_to BIGINT(20) UNSIGNED NULL,
            typing_started_at DATETIME NULL,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY status (status),
            KEY last_seen (last_seen)
        ) $charset_collate;";

        // Message reactions table
        $reactions_table = $wpdb->prefix . 'dab_chat_message_reactions';
        $sql_reactions = "CREATE TABLE IF NOT EXISTS $reactions_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            message_id BIGINT(20) UNSIGNED NOT NULL,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            reaction VARCHAR(50) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_reaction (message_id, user_id, reaction),
            KEY message_id (message_id),
            KEY user_id (user_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_messages);
        dbDelta($sql_conversations);
        dbDelta($sql_user_status);
        dbDelta($sql_reactions);
    }

    /**
     * Send a message
     */
    public static function send_message($sender_id, $recipient_id, $message, $group_id = null, $message_type = 'text', $attachment_data = null) {
        global $wpdb;

        if (empty($message) && empty($attachment_data)) {
            return new WP_Error('empty_message', 'Message cannot be empty.');
        }

        // Validate users exist
        if (!self::user_exists($sender_id) || ($recipient_id && !self::user_exists($recipient_id))) {
            return new WP_Error('invalid_user', 'Invalid user.');
        }

        // Prepare message data
        $message_data = array(
            'sender_id' => $sender_id,
            'message' => sanitize_textarea_field($message),
            'message_type' => $message_type,
            'created_at' => current_time('mysql')
        );

        if ($group_id) {
            $message_data['group_id'] = $group_id;
        } else {
            $message_data['recipient_id'] = $recipient_id;
        }

        // Handle attachment
        if ($attachment_data) {
            $message_data['attachment_url'] = $attachment_data['url'];
            $message_data['attachment_name'] = $attachment_data['name'];
            $message_data['attachment_size'] = $attachment_data['size'];
        }

        $messages_table = $wpdb->prefix . 'dab_chat_messages';
        $result = $wpdb->insert($messages_table, $message_data);

        if ($result === false) {
            return new WP_Error('send_failed', 'Failed to send message.');
        }

        $message_id = $wpdb->insert_id;

        // Update or create conversation for direct messages
        if (!$group_id && $recipient_id) {
            self::update_conversation($sender_id, $recipient_id, $message_id);
        }

        // Get the complete message data
        $complete_message = self::get_message_by_id($message_id);

        return $complete_message;
    }

    /**
     * Get messages for a conversation or group
     */
    public static function get_messages($user_id, $recipient_id = null, $group_id = null, $limit = 50, $offset = 0, $since_id = null) {
        global $wpdb;

        $messages_table = $wpdb->prefix . 'dab_chat_messages';
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $where_conditions = array("m.is_deleted = 0");
        $where_values = array();

        if ($group_id) {
            $where_conditions[] = "m.group_id = %d";
            $where_values[] = $group_id;
        } else if ($recipient_id) {
            $where_conditions[] = "((m.sender_id = %d AND m.recipient_id = %d) OR (m.sender_id = %d AND m.recipient_id = %d))";
            $where_values = array_merge($where_values, array($user_id, $recipient_id, $recipient_id, $user_id));
        } else {
            return array();
        }

        if ($since_id) {
            $where_conditions[] = "m.id > %d";
            $where_values[] = $since_id;
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        $limit_clause = sprintf('LIMIT %d OFFSET %d', $limit, $offset);

        $query = "
            SELECT m.*,
                   u.username as sender_username,
                   u.first_name as sender_first_name,
                   u.last_name as sender_last_name,
                   u.avatar_url as sender_avatar
            FROM $messages_table m
            LEFT JOIN $users_table u ON m.sender_id = u.id
            $where_clause
            ORDER BY m.created_at DESC
            $limit_clause
        ";

        $messages = $wpdb->get_results($wpdb->prepare($query, $where_values));

        // Reverse to show oldest first
        return array_reverse($messages);
    }

    /**
     * Get conversations for a user
     */
    public static function get_conversations($user_id, $limit = 20) {
        global $wpdb;

        $conversations_table = $wpdb->prefix . 'dab_chat_conversations';
        $messages_table = $wpdb->prefix . 'dab_chat_messages';
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $query = "
            SELECT c.*,
                   CASE
                       WHEN c.user1_id = %d THEN u2.username
                       ELSE u1.username
                   END as other_username,
                   CASE
                       WHEN c.user1_id = %d THEN u2.first_name
                       ELSE u1.first_name
                   END as other_first_name,
                   CASE
                       WHEN c.user1_id = %d THEN u2.last_name
                       ELSE u1.last_name
                   END as other_last_name,
                   CASE
                       WHEN c.user1_id = %d THEN u2.avatar_url
                       ELSE u1.avatar_url
                   END as other_avatar,
                   CASE
                       WHEN c.user1_id = %d THEN u2.id
                       ELSE u1.id
                   END as other_user_id,
                   m.message as last_message,
                   m.message_type as last_message_type,
                   m.sender_id as last_sender_id,
                   (SELECT COUNT(*) FROM $messages_table
                    WHERE ((sender_id != %d AND recipient_id = %d) OR group_id = c.id)
                    AND is_read = 0 AND is_deleted = 0) as unread_count
            FROM $conversations_table c
            LEFT JOIN $users_table u1 ON c.user1_id = u1.id
            LEFT JOIN $users_table u2 ON c.user2_id = u2.id
            LEFT JOIN $messages_table m ON c.last_message_id = m.id
            WHERE (c.user1_id = %d OR c.user2_id = %d)
            AND ((c.user1_id = %d AND c.user1_deleted = 0) OR (c.user2_id = %d AND c.user2_deleted = 0))
            ORDER BY c.last_activity DESC
            LIMIT %d
        ";

        return $wpdb->get_results($wpdb->prepare($query,
            $user_id, $user_id, $user_id, $user_id, $user_id, $user_id, $user_id, $user_id, $user_id, $user_id, $user_id, $limit
        ));
    }

    /**
     * Mark messages as read
     */
    public static function mark_messages_read($user_id, $sender_id = null, $group_id = null) {
        global $wpdb;

        $messages_table = $wpdb->prefix . 'dab_chat_messages';

        $where_conditions = array("is_read = 0", "is_deleted = 0");
        $where_values = array();

        if ($group_id) {
            $where_conditions[] = "group_id = %d";
            $where_values[] = $group_id;
        } else if ($sender_id) {
            $where_conditions[] = "sender_id = %d AND recipient_id = %d";
            $where_values = array_merge($where_values, array($sender_id, $user_id));
        }

        if (!empty($where_values)) {
            $where_clause = implode(' AND ', $where_conditions);
            $query = "UPDATE $messages_table SET is_read = 1 WHERE $where_clause";
            return $wpdb->query($wpdb->prepare($query, $where_values));
        }

        return false;
    }

    /**
     * Update or create conversation
     */
    private static function update_conversation($user1_id, $user2_id, $message_id) {
        global $wpdb;

        // Ensure consistent ordering
        if ($user1_id > $user2_id) {
            $temp = $user1_id;
            $user1_id = $user2_id;
            $user2_id = $temp;
        }

        $conversations_table = $wpdb->prefix . 'dab_chat_conversations';

        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM $conversations_table WHERE user1_id = %d AND user2_id = %d",
            $user1_id, $user2_id
        ));

        if ($existing) {
            // Update existing conversation
            $wpdb->update($conversations_table,
                array(
                    'last_message_id' => $message_id,
                    'last_activity' => current_time('mysql'),
                    'user1_deleted' => 0,
                    'user2_deleted' => 0
                ),
                array('id' => $existing->id)
            );
        } else {
            // Create new conversation
            $wpdb->insert($conversations_table, array(
                'user1_id' => $user1_id,
                'user2_id' => $user2_id,
                'last_message_id' => $message_id,
                'last_activity' => current_time('mysql')
            ));
        }
    }

    /**
     * Get message by ID
     */
    public static function get_message_by_id($message_id) {
        global $wpdb;

        $messages_table = $wpdb->prefix . 'dab_chat_messages';
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT m.*,
                    u.username as sender_username,
                    u.first_name as sender_first_name,
                    u.last_name as sender_last_name,
                    u.avatar_url as sender_avatar
             FROM $messages_table m
             LEFT JOIN $users_table u ON m.sender_id = u.id
             WHERE m.id = %d AND m.is_deleted = 0",
            $message_id
        ));
    }

    /**
     * Check if user exists
     */
    private static function user_exists($user_id) {
        global $wpdb;

        $users_table = $wpdb->prefix . 'dab_frontend_users';
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $users_table WHERE id = %d AND status = 'active'",
            $user_id
        ));

        return $count > 0;
    }

    /**
     * AJAX handler for sending messages
     */
    public static function ajax_send_message() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in to send messages');
            return;
        }

        $recipient_id = intval($_POST['recipient_id'] ?? 0);
        $group_id = intval($_POST['group_id'] ?? 0);
        $message = sanitize_textarea_field($_POST['message'] ?? '');
        $message_type = sanitize_text_field($_POST['message_type'] ?? 'text');

        if (!$recipient_id && !$group_id) {
            wp_send_json_error('Recipient or group is required');
            return;
        }

        if (empty($message)) {
            wp_send_json_error('Message cannot be empty');
            return;
        }

        $result = self::send_message($current_user->id, $recipient_id, $message, $group_id, $message_type);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'message' => 'Message sent successfully',
                'data' => $result
            ));
        }
    }

    /**
     * AJAX handler for getting messages
     */
    public static function ajax_get_messages() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $recipient_id = intval($_POST['recipient_id'] ?? 0);
        $group_id = intval($_POST['group_id'] ?? 0);
        $limit = intval($_POST['limit'] ?? 50);
        $offset = intval($_POST['offset'] ?? 0);
        $since_id = intval($_POST['since_id'] ?? 0);

        $messages = self::get_messages($current_user->id, $recipient_id, $group_id, $limit, $offset, $since_id ?: null);

        // Mark messages as read
        if ($recipient_id) {
            self::mark_messages_read($current_user->id, $recipient_id);
        } elseif ($group_id) {
            self::mark_messages_read($current_user->id, null, $group_id);
        }

        wp_send_json_success($messages);
    }

    /**
     * AJAX handler for getting conversations
     */
    public static function ajax_get_conversations() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $limit = intval($_POST['limit'] ?? 20);
        $conversations = self::get_conversations($current_user->id, $limit);

        wp_send_json_success($conversations);
    }

    /**
     * AJAX handler for marking messages as read
     */
    public static function ajax_mark_messages_read() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $sender_id = intval($_POST['sender_id'] ?? 0);
        $group_id = intval($_POST['group_id'] ?? 0);

        $result = self::mark_messages_read($current_user->id, $sender_id ?: null, $group_id ?: null);

        if ($result !== false) {
            wp_send_json_success('Messages marked as read');
        } else {
            wp_send_json_error('Failed to mark messages as read');
        }
    }

    /**
     * AJAX handler for deleting messages
     */
    public static function ajax_delete_message() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $message_id = intval($_POST['message_id'] ?? 0);
        if (!$message_id) {
            wp_send_json_error('Message ID is required');
            return;
        }

        global $wpdb;
        $messages_table = $wpdb->prefix . 'dab_chat_messages';

        // Check if user owns the message
        $message = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $messages_table WHERE id = %d AND sender_id = %d",
            $message_id, $current_user->id
        ));

        if (!$message) {
            wp_send_json_error('Message not found or you do not have permission to delete it');
            return;
        }

        // Soft delete the message
        $result = $wpdb->update($messages_table,
            array('is_deleted' => 1),
            array('id' => $message_id)
        );

        if ($result !== false) {
            wp_send_json_success('Message deleted successfully');
        } else {
            wp_send_json_error('Failed to delete message');
        }
    }

    /**
     * AJAX handler for searching users
     */
    public static function ajax_search_users() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $search_term = sanitize_text_field($_POST['search'] ?? '');
        if (strlen($search_term) < 2) {
            wp_send_json_error('Search term must be at least 2 characters');
            return;
        }

        global $wpdb;
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $users = $wpdb->get_results($wpdb->prepare(
            "SELECT id, username, first_name, last_name, avatar_url
             FROM $users_table
             WHERE (username LIKE %s OR first_name LIKE %s OR last_name LIKE %s)
             AND id != %d AND status = 'active'
             ORDER BY username ASC
             LIMIT 10",
            '%' . $wpdb->esc_like($search_term) . '%',
            '%' . $wpdb->esc_like($search_term) . '%',
            '%' . $wpdb->esc_like($search_term) . '%',
            $current_user->id
        ));

        wp_send_json_success($users);
    }

    /**
     * AJAX handler for getting user status
     */
    public static function ajax_get_user_status() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $user_ids = array_map('intval', $_POST['user_ids'] ?? array());
        if (empty($user_ids)) {
            wp_send_json_success(array());
            return;
        }

        global $wpdb;
        $status_table = $wpdb->prefix . 'dab_chat_user_status';

        $placeholders = implode(',', array_fill(0, count($user_ids), '%d'));
        $statuses = $wpdb->get_results($wpdb->prepare(
            "SELECT user_id, status, status_message, last_seen FROM $status_table WHERE user_id IN ($placeholders)",
            $user_ids
        ));

        wp_send_json_success($statuses);
    }

    /**
     * AJAX handler for updating user status
     */
    public static function ajax_update_user_status() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $status = sanitize_text_field($_POST['status'] ?? 'online');
        $status_message = sanitize_text_field($_POST['status_message'] ?? '');

        global $wpdb;
        $status_table = $wpdb->prefix . 'dab_chat_user_status';

        $result = $wpdb->replace($status_table, array(
            'user_id' => $current_user->id,
            'status' => $status,
            'status_message' => $status_message,
            'last_seen' => current_time('mysql')
        ));

        if ($result !== false) {
            wp_send_json_success('Status updated successfully');
        } else {
            wp_send_json_error('Failed to update status');
        }
    }

    /**
     * AJAX handler for updating last seen
     */
    public static function ajax_update_last_seen() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        global $wpdb;
        $status_table = $wpdb->prefix . 'dab_chat_user_status';

        $wpdb->replace($status_table, array(
            'user_id' => $current_user->id,
            'status' => 'online',
            'last_seen' => current_time('mysql')
        ));

        wp_send_json_success('Last seen updated');
    }
}
