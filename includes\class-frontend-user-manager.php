<?php
/**
 * Frontend User Manager
 *
 * Handles frontend user registration, login, and profile management
 * independent of WordPress admin system
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Frontend_User_Manager {

    /**
     * Initialize the class
     */
    public static function init() {
        // Create tables if they don't exist
        self::create_tables();

        // Register AJAX handlers
        add_action('wp_ajax_dab_frontend_register', array(__CLASS__, 'ajax_register_user'));
        add_action('wp_ajax_nopriv_dab_frontend_register', array(__CLASS__, 'ajax_register_user'));

        add_action('wp_ajax_dab_frontend_login', array(__CLASS__, 'ajax_login_user'));
        add_action('wp_ajax_nopriv_dab_frontend_login', array(__CLASS__, 'ajax_login_user'));

        add_action('wp_ajax_dab_frontend_logout', array(__CLASS__, 'ajax_logout_user'));
        add_action('wp_ajax_nopriv_dab_frontend_logout', array(__CLASS__, 'ajax_logout_user'));

        // Admin AJAX handlers
        add_action('wp_ajax_dab_get_user_stats', array(__CLASS__, 'ajax_get_user_stats'));
        add_action('wp_ajax_dab_export_users', array(__CLASS__, 'ajax_export_users'));
        add_action('wp_ajax_dab_send_verification_email', array(__CLASS__, 'ajax_send_verification_email'));

        add_action('wp_ajax_dab_update_profile', array(__CLASS__, 'ajax_update_profile'));
        add_action('wp_ajax_nopriv_dab_update_profile', array(__CLASS__, 'ajax_update_profile'));

        add_action('wp_ajax_dab_reset_password', array(__CLASS__, 'ajax_reset_password'));
        add_action('wp_ajax_nopriv_dab_reset_password', array(__CLASS__, 'ajax_reset_password'));

        add_action('wp_ajax_dab_verify_email', array(__CLASS__, 'ajax_verify_email'));
        add_action('wp_ajax_nopriv_dab_verify_email', array(__CLASS__, 'ajax_verify_email'));

        add_action('wp_ajax_dab_change_password', array(__CLASS__, 'ajax_change_password'));
        add_action('wp_ajax_nopriv_dab_change_password', array(__CLASS__, 'ajax_change_password'));

        // Start session for frontend users
        add_action('init', array(__CLASS__, 'start_session'));
    }

    /**
     * Start session for frontend users
     */
    public static function start_session() {
        if (!session_id()) {
            session_start();
        }
    }

    /**
     * Create necessary tables for frontend users
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Frontend users table
        $users_table = $wpdb->prefix . 'dab_frontend_users';
        $sql_users = "CREATE TABLE IF NOT EXISTS $users_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            username VARCHAR(60) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NULL,
            last_name VARCHAR(50) NULL,
            phone VARCHAR(20) NULL,
            avatar_url VARCHAR(255) NULL,
            role VARCHAR(50) DEFAULT 'user',
            status VARCHAR(20) DEFAULT 'active',
            email_verified TINYINT(1) DEFAULT 0,
            verification_token VARCHAR(255) NULL,
            reset_token VARCHAR(255) NULL,
            reset_token_expires DATETIME NULL,
            last_login DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY email (email),
            KEY username (username),
            KEY status (status)
        ) $charset_collate;";

        // User sessions table
        $sessions_table = $wpdb->prefix . 'dab_user_sessions';
        $sql_sessions = "CREATE TABLE IF NOT EXISTS $sessions_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            session_token VARCHAR(255) NOT NULL UNIQUE,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY session_token (session_token),
            KEY expires_at (expires_at)
        ) $charset_collate;";

        // User meta table for additional user data
        $user_meta_table = $wpdb->prefix . 'dab_user_meta';
        $sql_user_meta = "CREATE TABLE IF NOT EXISTS $user_meta_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            meta_key VARCHAR(255) NOT NULL,
            meta_value LONGTEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY meta_key (meta_key)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_users);
        dbDelta($sql_sessions);
        dbDelta($sql_user_meta);
    }

    /**
     * Register a new frontend user
     */
    public static function register_user($username, $email, $password, $first_name = '', $last_name = '') {
        global $wpdb;

        // Validate input
        if (empty($username) || empty($email) || empty($password)) {
            return new WP_Error('missing_fields', 'Username, email, and password are required.');
        }

        if (!is_email($email)) {
            return new WP_Error('invalid_email', 'Please enter a valid email address.');
        }

        if (strlen($password) < 6) {
            return new WP_Error('weak_password', 'Password must be at least 6 characters long.');
        }

        // Check if username or email already exists
        $users_table = $wpdb->prefix . 'dab_frontend_users';
        $existing_user = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM $users_table WHERE username = %s OR email = %s",
            $username, $email
        ));

        if ($existing_user) {
            return new WP_Error('user_exists', 'Username or email already exists.');
        }

        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Generate verification token
        $verification_token = wp_generate_password(32, false);

        // Insert user
        $result = $wpdb->insert($users_table, array(
            'username' => sanitize_user($username),
            'email' => sanitize_email($email),
            'password' => $hashed_password,
            'first_name' => sanitize_text_field($first_name),
            'last_name' => sanitize_text_field($last_name),
            'verification_token' => $verification_token,
            'created_at' => current_time('mysql')
        ));

        if ($result === false) {
            return new WP_Error('registration_failed', 'Failed to create user account.');
        }

        $user_id = $wpdb->insert_id;

        // Send verification email
        self::send_verification_email($user_id, $email, $verification_token);

        return $user_id;
    }

    /**
     * Login a frontend user
     */
    public static function login_user($username_or_email, $password, $remember = false) {
        global $wpdb;

        if (empty($username_or_email) || empty($password)) {
            return new WP_Error('missing_credentials', 'Username/email and password are required.');
        }

        // Get user by username or email
        $users_table = $wpdb->prefix . 'dab_frontend_users';
        $user = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $users_table WHERE (username = %s OR email = %s) AND status = 'active'",
            $username_or_email, $username_or_email
        ));

        if (!$user) {
            return new WP_Error('invalid_credentials', 'Invalid username/email or password.');
        }

        // Verify password
        if (!password_verify($password, $user->password)) {
            return new WP_Error('invalid_credentials', 'Invalid username/email or password.');
        }

        // Create session
        $session_token = self::create_session($user->id, $remember);

        if (is_wp_error($session_token)) {
            return $session_token;
        }

        // Update last login
        $wpdb->update($users_table,
            array('last_login' => current_time('mysql')),
            array('id' => $user->id)
        );

        return array(
            'user' => $user,
            'session_token' => $session_token
        );
    }

    /**
     * Create a user session
     */
    public static function create_session($user_id, $remember = false) {
        global $wpdb;

        $sessions_table = $wpdb->prefix . 'dab_user_sessions';
        $session_token = wp_generate_password(64, false);

        // Set expiration (24 hours or 30 days if remember me)
        $expires = $remember ? (time() + (30 * DAY_IN_SECONDS)) : (time() + DAY_IN_SECONDS);
        $expires_at = date('Y-m-d H:i:s', $expires);

        $result = $wpdb->insert($sessions_table, array(
            'user_id' => $user_id,
            'session_token' => $session_token,
            'ip_address' => self::get_client_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'expires_at' => $expires_at
        ));

        if ($result === false) {
            return new WP_Error('session_failed', 'Failed to create session.');
        }

        // Set session cookie
        setcookie('dab_session_token', $session_token, $expires, COOKIEPATH, COOKIE_DOMAIN, is_ssl(), true);
        $_SESSION['dab_user_id'] = $user_id;
        $_SESSION['dab_session_token'] = $session_token;

        return $session_token;
    }

    /**
     * Get current logged in user
     */
    public static function get_current_user() {
        $session_token = isset($_COOKIE['dab_session_token']) ? $_COOKIE['dab_session_token'] :
                        (isset($_SESSION['dab_session_token']) ? $_SESSION['dab_session_token'] : '');

        if (empty($session_token)) {
            return false;
        }

        return self::get_user_by_session($session_token);
    }

    /**
     * Get user by session token
     */
    public static function get_user_by_session($session_token) {
        global $wpdb;

        $sessions_table = $wpdb->prefix . 'dab_user_sessions';
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $user = $wpdb->get_row($wpdb->prepare(
            "SELECT u.* FROM $users_table u
             INNER JOIN $sessions_table s ON u.id = s.user_id
             WHERE s.session_token = %s AND s.expires_at > NOW() AND u.status = 'active'",
            $session_token
        ));

        return $user ? $user : false;
    }

    /**
     * Logout user
     */
    public static function logout_user($session_token = null) {
        global $wpdb;

        if (!$session_token) {
            $session_token = isset($_COOKIE['dab_session_token']) ? $_COOKIE['dab_session_token'] :
                            (isset($_SESSION['dab_session_token']) ? $_SESSION['dab_session_token'] : '');
        }

        if ($session_token) {
            // Delete session from database
            $sessions_table = $wpdb->prefix . 'dab_user_sessions';
            $wpdb->delete($sessions_table, array('session_token' => $session_token));
        }

        // Clear session data
        unset($_SESSION['dab_user_id']);
        unset($_SESSION['dab_session_token']);

        // Clear cookie
        setcookie('dab_session_token', '', time() - 3600, COOKIEPATH, COOKIE_DOMAIN);

        return true;
    }

    /**
     * Get client IP address
     */
    private static function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }

    /**
     * Send verification email
     */
    private static function send_verification_email($user_id, $email, $token) {
        $verification_url = add_query_arg(array(
            'action' => 'dab_verify_email',
            'token' => $token,
            'user_id' => $user_id
        ), admin_url('admin-ajax.php'));

        $subject = 'Verify your email address';
        $message = "Please click the following link to verify your email address:\n\n" . $verification_url;

        wp_mail($email, $subject, $message);
    }

    /**
     * AJAX handler for user registration
     */
    public static function ajax_register_user() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $username = sanitize_user($_POST['username'] ?? '');
        $email = sanitize_email($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $first_name = sanitize_text_field($_POST['first_name'] ?? '');
        $last_name = sanitize_text_field($_POST['last_name'] ?? '');

        $result = self::register_user($username, $email, $password, $first_name, $last_name);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'message' => 'Registration successful! Please check your email to verify your account.',
                'user_id' => $result
            ));
        }
    }

    /**
     * AJAX handler for user login
     */
    public static function ajax_login_user() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $username_or_email = sanitize_text_field($_POST['username_or_email'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']) && $_POST['remember'] === 'true';

        $result = self::login_user($username_or_email, $password, $remember);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'message' => 'Login successful!',
                'user' => array(
                    'id' => $result['user']->id,
                    'username' => $result['user']->username,
                    'email' => $result['user']->email,
                    'first_name' => $result['user']->first_name,
                    'last_name' => $result['user']->last_name
                ),
                'redirect_url' => home_url('/user-dashboard/')
            ));
        }
    }

    /**
     * AJAX handler for user logout
     */
    public static function ajax_logout_user() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $result = self::logout_user();

        if ($result) {
            wp_send_json_success(array(
                'message' => 'Logout successful!',
                'redirect_url' => home_url('/login/')
            ));
        } else {
            wp_send_json_error('Logout failed');
        }
    }

    /**
     * AJAX handler for profile update
     */
    public static function ajax_update_profile() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = self::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in to update your profile');
            return;
        }

        global $wpdb;
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $update_data = array();

        if (isset($_POST['first_name'])) {
            $update_data['first_name'] = sanitize_text_field($_POST['first_name']);
        }

        if (isset($_POST['last_name'])) {
            $update_data['last_name'] = sanitize_text_field($_POST['last_name']);
        }

        if (isset($_POST['phone'])) {
            $update_data['phone'] = sanitize_text_field($_POST['phone']);
        }

        if (isset($_POST['email'])) {
            $new_email = sanitize_email($_POST['email']);
            if ($new_email !== $current_user->email) {
                // Check if email already exists
                $existing = $wpdb->get_var($wpdb->prepare(
                    "SELECT id FROM $users_table WHERE email = %s AND id != %d",
                    $new_email, $current_user->id
                ));

                if ($existing) {
                    wp_send_json_error('Email address already in use');
                    return;
                }

                $update_data['email'] = $new_email;
                $update_data['email_verified'] = 0; // Reset verification status
            }
        }

        if (!empty($update_data)) {
            $update_data['updated_at'] = current_time('mysql');

            $result = $wpdb->update($users_table, $update_data, array('id' => $current_user->id));

            if ($result !== false) {
                wp_send_json_success('Profile updated successfully');
            } else {
                wp_send_json_error('Failed to update profile');
            }
        } else {
            wp_send_json_error('No data to update');
        }
    }

    /**
     * AJAX handler for password reset
     */
    public static function ajax_reset_password() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $email = sanitize_email($_POST['email'] ?? '');

        if (empty($email)) {
            wp_send_json_error('Email address is required');
            return;
        }

        global $wpdb;
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $user = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $users_table WHERE email = %s AND status = 'active'",
            $email
        ));

        if (!$user) {
            wp_send_json_error('No account found with that email address');
            return;
        }

        // Generate reset token
        $reset_token = wp_generate_password(32, false);
        $expires = date('Y-m-d H:i:s', time() + HOUR_IN_SECONDS); // 1 hour expiry

        $result = $wpdb->update($users_table,
            array(
                'reset_token' => $reset_token,
                'reset_token_expires' => $expires
            ),
            array('id' => $user->id)
        );

        if ($result !== false) {
            // Send reset email
            $reset_url = add_query_arg(array(
                'action' => 'reset_password',
                'token' => $reset_token,
                'user_id' => $user->id
            ), home_url('/reset-password/'));

            $subject = 'Password Reset Request';
            $message = "Click the following link to reset your password:\n\n" . $reset_url . "\n\nThis link will expire in 1 hour.";

            wp_mail($email, $subject, $message);

            wp_send_json_success('Password reset link sent to your email');
        } else {
            wp_send_json_error('Failed to generate reset token');
        }
    }

    /**
     * AJAX handler for email verification
     */
    public static function ajax_verify_email() {
        $token = sanitize_text_field($_GET['token'] ?? '');
        $user_id = intval($_GET['user_id'] ?? 0);

        if (empty($token) || !$user_id) {
            wp_die('Invalid verification link');
        }

        global $wpdb;
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $user = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $users_table WHERE id = %d AND verification_token = %s",
            $user_id, $token
        ));

        if (!$user) {
            wp_die('Invalid or expired verification link');
        }

        // Update user as verified
        $result = $wpdb->update($users_table,
            array(
                'email_verified' => 1,
                'verification_token' => null,
                'status' => 'active'
            ),
            array('id' => $user_id)
        );

        if ($result !== false) {
            wp_redirect(home_url('/login/?verified=1'));
            exit;
        } else {
            wp_die('Failed to verify email');
        }
    }

    /**
     * AJAX handler for password change
     */
    public static function ajax_change_password() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = self::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in to change your password');
            return;
        }

        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';

        if (empty($current_password) || empty($new_password)) {
            wp_send_json_error('Current password and new password are required');
            return;
        }

        // Verify current password
        if (!password_verify($current_password, $current_user->password)) {
            wp_send_json_error('Current password is incorrect');
            return;
        }

        // Validate new password length
        if (strlen($new_password) < 6) {
            wp_send_json_error('New password must be at least 6 characters long');
            return;
        }

        // Hash new password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

        // Update password in database
        global $wpdb;
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $result = $wpdb->update($users_table,
            array(
                'password' => $hashed_password,
                'updated_at' => current_time('mysql')
            ),
            array('id' => $current_user->id)
        );

        if ($result !== false) {
            wp_send_json_success('Password changed successfully');
        } else {
            wp_send_json_error('Failed to change password');
        }
    }

    /**
     * Get user statistics for admin dashboard
     */
    public static function get_user_statistics() {
        global $wpdb;
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $stats = array();

        // Total users
        $stats['total_users'] = $wpdb->get_var("SELECT COUNT(*) FROM $users_table");

        // Active users
        $stats['active_users'] = $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE status = 'active'");

        // Pending users
        $stats['pending_users'] = $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE status = 'pending'");

        // Verified users
        $stats['verified_users'] = $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE email_verified = 1");

        // Users registered today
        $stats['today_registrations'] = $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE DATE(created_at) = CURDATE()");

        // Users registered this week
        $stats['week_registrations'] = $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");

        // Users registered this month
        $stats['month_registrations'] = $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");

        // Role distribution
        $role_stats = $wpdb->get_results("SELECT role, COUNT(*) as count FROM $users_table WHERE role IS NOT NULL GROUP BY role");
        $stats['role_distribution'] = array();
        foreach ($role_stats as $role_stat) {
            $stats['role_distribution'][$role_stat->role] = $role_stat->count;
        }

        return $stats;
    }

    /**
     * AJAX handler for getting user statistics
     */
    public static function ajax_get_user_stats() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $stats = self::get_user_statistics();
        wp_send_json_success($stats);
    }

    /**
     * Export users to CSV
     */
    public static function export_users_csv() {
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        global $wpdb;
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $users = $wpdb->get_results("SELECT * FROM $users_table ORDER BY created_at DESC");

        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="frontend-users-' . date('Y-m-d') . '.csv"');

        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, array(
            'ID', 'Username', 'Email', 'First Name', 'Last Name', 'Role', 'Status',
            'Email Verified', 'Created At', 'Updated At', 'Last Login'
        ));

        // CSV data
        foreach ($users as $user) {
            fputcsv($output, array(
                $user->id,
                $user->username,
                $user->email,
                $user->first_name,
                $user->last_name,
                $user->role,
                $user->status,
                $user->email_verified ? 'Yes' : 'No',
                $user->created_at,
                $user->updated_at,
                $user->last_login
            ));
        }

        fclose($output);
        exit;
    }

    /**
     * AJAX handler for exporting users
     */
    public static function ajax_export_users() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Redirect to export function
        wp_redirect(admin_url('admin-ajax.php?action=dab_export_users_csv'));
        exit;
    }

    /**
     * Send verification email to user
     */
    public static function send_verification_email_to_user($user_id) {
        global $wpdb;
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $user = $wpdb->get_row($wpdb->prepare("SELECT * FROM $users_table WHERE id = %d", $user_id));

        if (!$user) {
            return new WP_Error('user_not_found', 'User not found');
        }

        // Generate new verification token
        $verification_token = wp_generate_password(32, false);

        $result = $wpdb->update($users_table,
            array('verification_token' => $verification_token),
            array('id' => $user_id)
        );

        if ($result !== false) {
            self::send_verification_email($user_id, $user->email, $verification_token);
            return true;
        }

        return new WP_Error('update_failed', 'Failed to update verification token');
    }

    /**
     * AJAX handler for sending verification email
     */
    public static function ajax_send_verification_email() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'dab_frontend_users_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $user_id = intval($_POST['user_id']);

        $result = self::send_verification_email_to_user($user_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success('Verification email sent successfully');
        }
    }

    /**
     * Get recent user activity
     */
    public static function get_recent_activity($limit = 10) {
        global $wpdb;
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        $recent_users = $wpdb->get_results($wpdb->prepare(
            "SELECT username, email, created_at, status FROM $users_table
             ORDER BY created_at DESC LIMIT %d",
            $limit
        ));

        return $recent_users;
    }

    /**
     * Get user login sessions
     */
    public static function get_user_sessions($user_id = null) {
        global $wpdb;
        $sessions_table = $wpdb->prefix . 'dab_user_sessions';

        $where_clause = '';
        $params = array();

        if ($user_id) {
            $where_clause = 'WHERE user_id = %d';
            $params[] = $user_id;
        }

        $query = "SELECT s.*, u.username, u.email
                  FROM $sessions_table s
                  LEFT JOIN {$wpdb->prefix}dab_frontend_users u ON s.user_id = u.id
                  $where_clause
                  ORDER BY s.created_at DESC";

        if (!empty($params)) {
            return $wpdb->get_results($wpdb->prepare($query, $params));
        } else {
            return $wpdb->get_results($query);
        }
    }

    /**
     * Clean up expired sessions
     */
    public static function cleanup_expired_sessions() {
        global $wpdb;
        $sessions_table = $wpdb->prefix . 'dab_user_sessions';

        $wpdb->query("DELETE FROM $sessions_table WHERE expires_at < NOW()");
    }
}
