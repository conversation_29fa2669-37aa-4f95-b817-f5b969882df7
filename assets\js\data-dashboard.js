/**
 * Enhanced Data Management Dashboard JavaScript
 */
(function($) {
    'use strict';

    // DataTable instance
    let dataTable = null;

    // Chart instance
    let dataChart = null;

    // Current table ID
    let currentTableId = null;

    // Current table fields
    let tableFields = [];

    // Active filters
    let activeFilters = [];

    /**
     * Initialize the dashboard
     */
    function initDashboard() {
        // Check if we have a table ID in the URL
        const urlParams = new URLSearchParams(window.location.search);
        const tableId = urlParams.get('table_id');

        if (tableId) {
            // Select the table
            selectTable(tableId);
        }

        // Add event listeners
        addEventListeners();
    }

    /**
     * Add event listeners
     */
    function addEventListeners() {
        // Table selection
        $('.dab-table-item').on('click', function() {
            const tableId = $(this).data('table-id');
            selectTable(tableId);
        });

        // Table search
        $('#dab-table-search').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();

            $('.dab-table-item').each(function() {
                const tableName = $(this).find('.dab-table-name').text().toLowerCase();

                if (tableName.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // Refresh tables
        $('.dab-refresh-tables').on('click', function() {
            location.reload();
        });

        // Refresh data
        $('.dab-refresh-data').on('click', function() {
            if (currentTableId) {
                loadTableData(currentTableId);
            }
        });

        // Toggle filters
        $('.dab-toggle-filters').on('click', function() {
            $('.dab-filters-section').slideToggle();
            $('.dab-visualization-section').slideUp();
            $('.dab-import-export-section').slideUp();
        });

        // Toggle visualization
        $('.dab-toggle-visualization').on('click', function() {
            $('.dab-visualization-section').slideToggle();
            $('.dab-filters-section').slideUp();
            $('.dab-import-export-section').slideUp();
        });

        // Toggle import/export
        $('.dab-toggle-import-export').on('click', function() {
            $('.dab-import-export-section').slideToggle();
            $('.dab-filters-section').slideUp();
            $('.dab-visualization-section').slideUp();
        });

        // Filter tab buttons
        $('.dab-filter-tabs .dab-tab-button').on('click', function() {
            const tab = $(this).data('tab');

            // Update active tab button
            $('.dab-filter-tabs .dab-tab-button').removeClass('active');
            $(this).addClass('active');

            // Show selected tab content
            $('.dab-tab-content').hide();
            $(`#dab-${tab}-filters-tab`).show();
        });

        // Import/Export tab buttons
        $('.dab-import-export-tabs .dab-tab-button').on('click', function() {
            const tab = $(this).data('tab');

            // Update active tab button
            $('.dab-import-export-tabs .dab-tab-button').removeClass('active');
            $(this).addClass('active');

            // Show selected tab content
            $('.dab-tab-content').hide();
            $(`#dab-${tab}-tab`).show();
        });

        // Add filter
        $('.dab-add-filter').on('click', function() {
            const field = $('#dab-filter-field').val();
            const operator = $('#dab-filter-operator').val();
            const value = $('#dab-filter-value').val();
            const value2 = $('#dab-filter-value2').val();

            if (field && operator) {
                // Check if operator requires a value
                if (['is_empty', 'is_not_empty'].includes(operator)) {
                    addFilter(field, operator, '');
                } else if (operator === 'between' && value && value2) {
                    addFilter(field, operator, value, value2);
                } else if (operator !== 'between' && value) {
                    addFilter(field, operator, value);
                }
            }
        });

        // Handle operator change to show/hide second value field
        $('#dab-filter-operator').on('change', function() {
            const operator = $(this).val();

            if (operator === 'between') {
                $('.dab-filter-value2-container').show();
            } else {
                $('.dab-filter-value2-container').hide();
            }

            if (['is_empty', 'is_not_empty'].includes(operator)) {
                $('#dab-filter-value-container').hide();
            } else {
                $('#dab-filter-value-container').show();
            }
        });

        // Clear all filters
        $('.dab-clear-all-filters').on('click', function() {
            clearAllFilters();
        });

        // Advanced filter controls
        $('.dab-add-filter-condition').on('click', function() {
            addFilterCondition($(this).closest('.dab-filter-group-container').find('.dab-filter-conditions'));
        });

        $('.dab-add-filter-group').on('click', function() {
            addFilterGroup();
        });

        $('.dab-apply-advanced-filters').on('click', function() {
            applyAdvancedFilters();
        });

        $('.dab-save-advanced-filters').on('click', function() {
            saveAdvancedFilters();
        });

        // Import/Export controls
        $('#dab-import-file').on('change', function() {
            handleFileUpload(this);
        });

        $('.dab-import-data').on('click', function() {
            importData();
        });

        $('.dab-export-data').on('click', function() {
            exportData();
        });

        // Bulk actions
        $('.dab-bulk-delete').on('click', function() {
            bulkDelete();
        });

        $('.dab-bulk-export').on('click', function() {
            bulkExport();
        });

        // Generate chart
        $('.dab-generate-chart').on('click', function() {
            const chartType = $('#dab-chart-type').val();
            const xAxis = $('#dab-chart-x-axis').val();
            const yAxis = $('#dab-chart-y-axis').val();

            if (chartType && xAxis && yAxis) {
                generateChart(chartType, xAxis, yAxis);
            }
        });

        // Add record
        $('.dab-add-record').off('click').on('click', function() {
            if (currentTableId) {
                console.log('Add record button clicked, opening modal with null record ID');
                openRecordModal(null);
            } else {
                console.log('Add record button clicked but no table is selected');
                showError('Please select a table first');
            }
        });

        // Note: Modal close and save buttons are now handled in openRecordModal
        // to ensure they're properly bound each time the modal is opened

        // Select all records
        $('#dab-select-all').on('click', function() {
            const isChecked = $(this).prop('checked');

            if (dataTable) {
                dataTable.rows().nodes().to$().find('input[type="checkbox"]').prop('checked', isChecked);
            }
        });
    }
    /**
     * Select a table
     *
     * @param {number} tableId The table ID
     */
    function selectTable(tableId) {
        // Update UI
        $('.dab-table-item').removeClass('active');
        $(`.dab-table-item[data-table-id="${tableId}"]`).addClass('active');

        // Set current table ID
        currentTableId = tableId;

        // Update URL
        const url = new URL(window.location.href);
        url.searchParams.set('table_id', tableId);
        window.history.pushState({}, '', url);

        // Get table name
        const tableName = $(`.dab-table-item[data-table-id="${tableId}"]`).find('.dab-table-name').text();
        $('#dab-current-table-name').text(tableName);

        // Load table data
        loadTableData(tableId);

        // Load table fields
        loadTableFields(tableId);
    }

    /**
     * Load table data
     *
     * @param {number} tableId The table ID
     */
    function loadTableData(tableId) {
        console.log('loadTableData called with tableId:', tableId);

        // Show loading overlay
        $('.dab-loading-overlay').show();

        // Destroy existing DataTable if any
        if (dataTable) {
            try {
                console.log('Destroying existing DataTable');
                dataTable.destroy();
                dataTable = null;
            } catch (e) {
                console.error('Error destroying DataTable:', e);
            }
        }

        // Clear the table completely to avoid column mismatch
        $('#dab-data-table').empty();
        // Recreate the basic table structure
        $('#dab-data-table').html('<thead><tr></tr></thead><tbody></tbody>');
        console.log('Table HTML structure reset');

        // Fetch data via AJAX
        console.log('Sending AJAX request to fetch table data');
        $.ajax({
            url: dab_data.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_get_dashboard_table_data',
                table_id: tableId,
                filters: activeFilters,
                nonce: dab_data.nonce
            },
            success: function(response) {
                console.log('AJAX response received:', response);
                if (response.success) {
                    console.log('Data fetch successful, rendering data table');
                    renderDataTable(response.data);
                } else {
                    console.error('Error in AJAX response:', response.data);
                    showError(response.data);
                    // Reset the table to a basic state if there's an error
                    $('#dab-data-table thead tr').html('<th>ID</th><th>No Data Available</th>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                showError(dab_data.i18n.error + ' (' + status + ': ' + error + ')');
                // Reset the table to a basic state if there's an error
                $('#dab-data-table thead tr').html('<th>ID</th><th>Error</th>');
            },
            complete: function() {
                // Hide loading overlay
                $('.dab-loading-overlay').hide();
                console.log('Data loading complete');
            }
        });
    }

    /**
     * Load table fields
     *
     * @param {number} tableId The table ID
     */
    function loadTableFields(tableId) {
        $.ajax({
            url: dab_data.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_get_dashboard_table_fields',
                table_id: tableId,
                nonce: dab_data.nonce
            },
            success: function(response) {
                if (response.success) {
                    tableFields = response.data;

                    // Update filter fields
                    updateFilterFields();

                    // Update chart fields
                    updateChartFields();
                }
            }
        });
    }

    /**
     * Update filter fields
     */
    function updateFilterFields() {
        const filterField = $('#dab-filter-field');

        // Clear existing options
        filterField.html('<option value="">Select Field</option>');

        // Add options for each field
        tableFields.forEach(function(field) {
            filterField.append(`<option value="${field.field_slug}">${field.field_label}</option>`);
        });
    }

    /**
     * Update chart fields
     */
    function updateChartFields() {
        const xAxis = $('#dab-chart-x-axis');
        const yAxis = $('#dab-chart-y-axis');

        // Clear existing options
        xAxis.html('<option value="">Select Field</option>');
        yAxis.html('<option value="">Select Field</option>');

        // Add options for each field
        tableFields.forEach(function(field) {
            xAxis.append(`<option value="${field.field_slug}">${field.field_label}</option>`);

            // Only add numeric fields to Y-axis
            if (['number', 'currency', 'formula', 'rollup'].includes(field.field_type)) {
                yAxis.append(`<option value="${field.field_slug}">${field.field_label}</option>`);
            }
        });
    }
    /**
     * Render data table
     *
     * @param {Object} data The data to render
     */
    function renderDataTable(data) {
        try {
            console.log('renderDataTable called with data:', data);

            // Validate data structure
            if (!data || typeof data !== 'object') {
                console.error('Invalid data format received:', data);
                throw new Error('Invalid data format received');
            }

            // Get table headers
            const headers = data.headers || [];
            const records = data.records || [];

            console.log('Rendering data table with headers:', headers);
            console.log('Number of records:', records.length);

            if (records.length > 0) {
                console.log('Sample record:', records[0]);
            }

            // Build table headers
            let headerHtml = '<th><input type="checkbox" id="dab-select-all"></th><th>ID</th>';

            headers.forEach(function(header) {
                headerHtml += `<th>${header.label}</th>`;
            });

            headerHtml += '<th>Actions</th>';

            // Set the headers
            $('#dab-data-table thead tr').html(headerHtml);
            console.log('Table headers set');

            // Build the columns configuration
            const columns = buildDataTableColumns(headers);

            console.log('DataTable columns configuration:', columns);
            console.log('Number of header columns:', headers.length + 3); // +3 for checkbox, ID, and actions
            console.log('Number of data columns:', columns.length);

            // Initialize DataTable with error handling
            try {
                console.log('Initializing DataTable with', records.length, 'records');

                // Check if the table element exists
                if ($('#dab-data-table').length === 0) {
                    console.error('Table element not found in DOM');
                    throw new Error('Table element not found');
                }

                // Verify data structure before initialization
                if (!Array.isArray(records)) {
                    console.error('Records is not an array:', records);
                    throw new Error('Records must be an array');
                }

                if (!Array.isArray(columns)) {
                    console.error('Columns is not an array:', columns);
                    throw new Error('Columns must be an array');
                }

                // Initialize DataTable
                dataTable = $('#dab-data-table').DataTable({
                    data: records,
                    responsive: true,
                    processing: true,
                    columns: columns,
                    destroy: true, // Ensure any existing instance is destroyed
                    dom: 'Bfrtip',
                    buttons: [
                        'copy', 'csv', 'excel', 'pdf'
                    ],
                    language: {
                        search: dab_data.i18n.search,
                        processing: dab_data.i18n.processing,
                        zeroRecords: dab_data.i18n.no_records,
                        paginate: {
                            first: dab_data.i18n.first,
                            last: dab_data.i18n.last,
                            next: dab_data.i18n.next,
                            previous: dab_data.i18n.previous
                        }
                    }
                });

                console.log('DataTable initialized successfully');
            } catch (dtError) {
                console.error('Error initializing DataTable:', dtError);
                console.error('Error details:', dtError.stack);
                // Fallback to a simpler table if DataTable initialization fails
                $('#dab-data-table').empty().html('<thead><tr><th>ID</th><th>Data</th></tr></thead><tbody><tr><td colspan="2">Error initializing table. Please try refreshing the page.</td></tr></tbody>');
                return;
            }

            // Re-add event listener for select all
            $('#dab-select-all').off('click').on('click', function() {
                const isChecked = $(this).prop('checked');
                dataTable.rows().nodes().to$().find('input[type="checkbox"]').prop('checked', isChecked);
            });

            // Remove any existing event handlers to prevent duplicates
            $('#dab-data-table').off('click', '.dab-edit-btn');
            $('#dab-data-table').off('click', '.dab-delete-btn');

            // Add event listeners for edit and delete buttons
            $('#dab-data-table').on('click', '.dab-edit-btn', function() {
                const recordId = $(this).data('id');
                openRecordModal(recordId);
            });

            $('#dab-data-table').on('click', '.dab-delete-btn', function() {
                const recordId = $(this).data('id');

                Swal.fire({
                    title: 'Are you sure?',
                    text: dab_data.i18n.delete_confirm,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Yes, delete it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        deleteRecord(recordId);
                    }
                });
            });
        } catch (error) {
            console.error('Error in renderDataTable:', error);
            showError('Error rendering table: ' + error.message);

            // Ensure the table is in a usable state
            $('#dab-data-table').empty().html('<thead><tr><th>Error</th></tr></thead><tbody><tr><td>Failed to render table data</td></tr></tbody>');
        }
    }

    /**
     * Build DataTable columns
     *
     * @param {Array} headers The table headers
     * @return {Array} The DataTable columns
     */
    function buildDataTableColumns(headers) {
        try {
            // Validate headers
            if (!Array.isArray(headers)) {
                console.error('Headers is not an array:', headers);
                headers = [];
            }

            const columns = [
                {
                    data: null,
                    orderable: false,
                    render: function() {
                        return '<input type="checkbox" name="selected_records[]">';
                    }
                },
                {
                    data: 'id',
                    render: function(data) {
                        // Ensure we have a valid ID
                        return data || '';
                    }
                }
            ];

            // Add columns for each header
            headers.forEach(function(header) {
                if (!header || !header.slug) {
                    console.error('Invalid header:', header);
                    return;
                }

                columns.push({
                    data: header.slug,
                    render: function(data, type, row) {
                        if (type === 'display') {
                            return formatFieldValue(data, header.type);
                        }
                        return data !== undefined ? data : '';
                    }
                });
            });

            // Add actions column
            columns.push({
                data: null,
                orderable: false,
                render: function(data, type, row) {
                    if (!row || !row.id) {
                        return '<span class="dab-action-error">Invalid row data</span>';
                    }

                    return `
                        <button type="button" class="dab-action-btn dab-edit-btn" data-id="${row.id}">
                            <span class="dashicons dashicons-edit"></span> Edit
                        </button>
                        <button type="button" class="dab-action-btn dab-delete-btn" data-id="${row.id}">
                            <span class="dashicons dashicons-trash"></span> Delete
                        </button>
                    `;
                }
            });

            return columns;
        } catch (error) {
            console.error('Error building DataTable columns:', error);
            // Return a minimal set of columns as fallback
            return [
                { data: null, render: function() { return ''; } },
                { data: 'id' },
                { data: null, render: function() { return 'Error loading data'; } }
            ];
        }
    }
    /**
     * Format field value for display
     *
     * @param {*} value The field value
     * @param {string} type The field type
     * @return {string} The formatted value
     */
    function formatFieldValue(value, type) {
        if (value === null || value === undefined) {
            return '';
        }

        switch (type) {
            case 'date':
                return new Date(value).toLocaleDateString();
            case 'checkbox':
                return value ? '✓' : '✗';
            case 'currency':
                return '$' + parseFloat(value).toFixed(2);
            default:
                return value;
        }
    }

    /**
     * Open record modal
     *
     * @param {number|null} recordId The record ID (null for new record)
     */
    function openRecordModal(recordId) {
        console.log('Opening record modal for record ID:', recordId);

        // Reset form
        $('#dab-record-form')[0].reset();
        $('.dab-form-validation-message').hide();

        // Set modal title
        $('#dab-modal-title').text(recordId ? 'Edit Record' : 'Add New Record');

        // Set record ID
        $('#dab-record-id').val(recordId || '');

        // Set table ID
        $('#dab-table-id').val(currentTableId);

        // Clear form fields
        $('#dab-form-fields').html('<div class="dab-loading">Loading form fields...</div>');

        // Add event listeners for modal actions
        $('.dab-modal-close, .dab-modal-cancel').off('click').on('click', function() {
            closeModal();
        });

        $('.dab-modal-save').off('click').on('click', function() {
            saveRecord();
        });

        // Add event listeners for form tabs
        $('.dab-form-tab').off('click').on('click', function() {
            const tab = $(this).data('tab');
            $('.dab-form-tab').removeClass('active');
            $(this).addClass('active');

            // Show/hide fields based on tab
            if (tab === 'basic') {
                $('.dab-basic-fields').show();
                $('.dab-advanced-fields').hide();
            } else {
                $('.dab-basic-fields').hide();
                $('.dab-advanced-fields').show();
            }
        });

        // If editing, load record data
        if (recordId) {
            console.log('Loading record data for editing, ID:', recordId);
            loadRecordData(recordId);
        } else {
            console.log('Generating empty form for new record');
            // Generate form fields
            generateFormFields();
        }

        // Show modal
        $('#dab-record-modal').show();
    }

    /**
     * Close modal
     */
    function closeModal() {
        $('#dab-record-modal').hide();
    }

    /**
     * Load record data
     *
     * @param {number} recordId The record ID
     */
    function loadRecordData(recordId) {
        // Show loading
        $('#dab-form-fields').html('<div class="dab-loading">Loading record data...</div>');

        // Fetch data via AJAX
        $.ajax({
            url: dab_data.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_get_dashboard_record',
                table_id: currentTableId,
                record_id: recordId,
                nonce: dab_data.nonce
            },
            success: function(response) {
                if (response.success) {
                    generateFormFields(response.data);
                } else {
                    $('#dab-form-fields').html(`<div class="dab-error">${response.data}</div>`);
                }
            },
            error: function() {
                $('#dab-form-fields').html(`<div class="dab-error">${dab_data.i18n.error}</div>`);
            }
        });
    }

    /**
     * Generate form fields
     *
     * @param {Object} recordData The record data (optional)
     */
    function generateFormFields(recordData) {
        let basicFieldsHtml = '';
        let advancedFieldsHtml = '';

        // Add event listeners for form tabs
        $('.dab-form-tab').off('click').on('click', function() {
            const tab = $(this).data('tab');

            // Update active tab button
            $('.dab-form-tab').removeClass('active');
            $(this).addClass('active');

            // Show/hide field groups
            if (tab === 'basic') {
                $('.dab-basic-fields').show();
                $('.dab-advanced-fields').hide();
            } else {
                $('.dab-basic-fields').hide();
                $('.dab-advanced-fields').show();
            }
        });

        // Generate fields based on table fields
        tableFields.forEach(function(field) {
            const value = recordData ? recordData[field.field_slug] : '';
            const isRequired = field.required ? true : false;
            const fieldHtml = `
                <div class="dab-form-field ${isRequired ? 'required' : ''}">
                    <label for="field-${field.field_slug}">${field.field_label}</label>
                    ${generateFieldInput(field, value)}
                    ${field.description ? `<div class="dab-field-description">${field.description}</div>` : ''}
                </div>
            `;

            // Determine if field is basic or advanced
            if (['id', 'name', 'title', 'description', 'status', 'date', 'email', 'phone'].includes(field.field_slug) ||
                field.field_type === 'text' ||
                field.required) {
                basicFieldsHtml += fieldHtml;
            } else {
                advancedFieldsHtml += fieldHtml;
            }
        });

        // Update form fields
        const html = `
            <div class="dab-basic-fields">
                ${basicFieldsHtml || '<p>No basic fields available.</p>'}
            </div>
            <div class="dab-advanced-fields" style="display: none;">
                ${advancedFieldsHtml || '<p>No advanced fields available.</p>'}
            </div>
        `;

        $('#dab-form-fields').html(html);
    }
    /**
     * Generate field input
     *
     * @param {Object} field The field object
     * @param {*} value The field value
     * @return {string} The input HTML
     */
    function generateFieldInput(field, value) {
        const required = field.required ? 'required' : '';
        const placeholder = field.placeholder || '';

        // Ensure value is properly escaped for HTML attributes
        const escapeHtml = (str) => {
            if (str === null || str === undefined) return '';
            return String(str)
                .replace(/&/g, '&amp;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;');
        };

        const escapedValue = escapeHtml(value);

        switch (field.field_type) {
            case 'text':
            case 'email':
            case 'number':
            case 'currency':
                return `<input type="${field.field_type === 'currency' ? 'number' : field.field_type}"
                               id="field-${field.field_slug}"
                               name="${field.field_slug}"
                               value="${escapedValue}"
                               ${required}
                               placeholder="${placeholder}"
                               ${field.field_type === 'number' || field.field_type === 'currency' ? 'step="0.01"' : ''}>`;
            case 'textarea':
                return `<textarea id="field-${field.field_slug}"
                                 name="${field.field_slug}"
                                 ${required}
                                 placeholder="${placeholder}">${escapedValue}</textarea>`;
            case 'select':
            case 'radio':
                let options = [];
                try {
                    options = field.options ? JSON.parse(field.options) : [];
                    // If options is not an array, convert it to an array
                    if (!Array.isArray(options)) {
                        options = Object.values(options);
                    }
                } catch (e) {
                    console.error('Error parsing options for field', field.field_slug, e);
                    options = [];
                }

                if (field.field_type === 'select') {
                    let selectHtml = `<select id="field-${field.field_slug}" name="${field.field_slug}" ${required}>`;
                    selectHtml += `<option value="">Select an option</option>`;

                    options.forEach(function(option) {
                        const selected = option === value ? 'selected' : '';
                        selectHtml += `<option value="${escapeHtml(option)}" ${selected}>${escapeHtml(option)}</option>`;
                    });

                    selectHtml += `</select>`;
                    return selectHtml;
                } else {
                    let radioHtml = `<div class="dab-radio-group">`;

                    options.forEach(function(option, index) {
                        const checked = option === value ? 'checked' : '';
                        radioHtml += `
                            <label class="dab-radio-label">
                                <input type="radio"
                                       name="${field.field_slug}"
                                       value="${escapeHtml(option)}"
                                       ${checked}
                                       ${required && index === 0 ? 'required' : ''}>
                                ${escapeHtml(option)}
                            </label>
                        `;
                    });

                    radioHtml += `</div>`;
                    return radioHtml;
                }
            case 'checkbox':
                const checked = value ? 'checked' : '';
                return `<input type="checkbox" id="field-${field.field_slug}" name="${field.field_slug}" value="1" ${checked}>`;
            case 'date':
                return `<input type="date" id="field-${field.field_slug}" name="${field.field_slug}" value="${escapedValue}" ${required}>`;
            default:
                return `<input type="text" id="field-${field.field_slug}" name="${field.field_slug}" value="${escapedValue}" ${required} placeholder="${placeholder}">`;
        }
    }

    /**
     * Save record
     */
    function saveRecord() {
        // Validate form
        if (!validateForm()) {
            return;
        }

        // Get form data
        const formData = new FormData(document.getElementById('dab-record-form'));

        // Debug: Log form data
        console.log('Form data being submitted:');
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + pair[1]);
        }

        // Add action
        formData.append('action', 'dab_save_dashboard_record');
        formData.append('nonce', dab_data.nonce);

        // Show loading
        $('.dab-modal-save').prop('disabled', true).text('Saving...');

        // Hide any previous validation messages
        $('.dab-form-validation-message').hide();

        // Send AJAX request
        $.ajax({
            url: dab_data.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('AJAX Success Response:', response);
                if (response.success) {
                    // Show success message
                    Swal.fire({
                        title: 'Success!',
                        text: response.data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });

                    // Close modal
                    closeModal();

                    // Reload data
                    loadTableData(currentTableId);
                } else {
                    // Show error message in the form
                    $('.dab-form-validation-message').html(response.data).show();

                    // Scroll to the error message
                    $('.dab-form-validation-message')[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            },
            error: function(xhr, status, error) {
                // Show error message
                $('.dab-form-validation-message').html('An error occurred while saving the record. Please try again.').show();
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);

                // Try to parse the response for more details
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    console.error('Parsed error response:', errorResponse);
                } catch (e) {
                    console.error('Could not parse error response');
                }
            },
            complete: function() {
                // Reset button
                $('.dab-modal-save').prop('disabled', false).text('Save Changes');
            }
        });
    }

    /**
     * Validate form
     *
     * @return {boolean} Whether the form is valid
     */
    function validateForm() {
        // Reset validation message
        $('.dab-form-validation-message').hide();

        // Check required fields
        let isValid = true;
        let errorMessage = '';

        // Find all required fields
        $('.dab-form-field.required').each(function() {
            const input = $(this).find('input, select, textarea');
            const label = $(this).find('label').text();

            // Skip checkboxes
            if (input.attr('type') === 'checkbox') {
                return;
            }

            // Check if field is empty
            if (!input.val()) {
                isValid = false;
                errorMessage += `<div>• ${label} is required</div>`;

                // Highlight the field
                input.css('border-color', '#dc3545');
            } else {
                // Reset border color
                input.css('border-color', '#ced4da');
            }
        });

        // Show validation message if form is invalid
        if (!isValid) {
            $('.dab-form-validation-message').html(`<strong>Please fix the following errors:</strong>${errorMessage}`).show();

            // Scroll to the error message
            $('.dab-form-validation-message')[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        return isValid;
    }
    /**
     * Delete a record
     *
     * @param {number} recordId The record ID to delete
     */
    function deleteRecord(recordId) {
        // Show loading
        $('.dab-loading-overlay').show();

        // Send AJAX request
        $.ajax({
            url: dab_data.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_delete_dashboard_record',
                table_id: currentTableId,
                record_id: recordId,
                nonce: dab_data.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    Swal.fire({
                        title: 'Deleted!',
                        text: 'Record has been deleted successfully.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });

                    // Reload data
                    loadTableData(currentTableId);
                } else {
                    // Show error message
                    showError(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                showError(dab_data.i18n.error + ' (' + status + ': ' + error + ')');
            },
            complete: function() {
                // Hide loading overlay
                $('.dab-loading-overlay').hide();
            }
        });
    }

    /**
     * Bulk delete selected records
     */
    function bulkDelete() {
        // Get selected records
        const selectedRecords = getSelectedRecords();

        if (selectedRecords.length === 0) {
            Swal.fire({
                title: 'No Records Selected',
                text: 'Please select at least one record to delete.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Confirm deletion
        Swal.fire({
            title: 'Are you sure?',
            text: dab_data.i18n.bulk_delete_confirm,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete them!'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading overlay
                $('.dab-loading-overlay').show();

                // Send AJAX request
                $.ajax({
                    url: dab_data.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'dab_bulk_delete_records',
                        table_id: currentTableId,
                        record_ids: selectedRecords,
                        nonce: dab_data.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            Swal.fire({
                                title: 'Deleted!',
                                text: response.data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });

                            // Reload data
                            loadTableData(currentTableId);
                        } else {
                            // Show error message
                            showError(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        console.error('Response:', xhr.responseText);
                        showError(dab_data.i18n.error + ' (' + status + ': ' + error + ')');
                    },
                    complete: function() {
                        // Hide loading overlay
                        $('.dab-loading-overlay').hide();
                    }
                });
            }
        });
    }
    /**
     * Bulk export selected records
     */
    function bulkExport() {
        // Get selected records
        const selectedRecords = getSelectedRecords();

        if (selectedRecords.length === 0) {
            Swal.fire({
                title: 'No Records Selected',
                text: 'Please select at least one record to export.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Show export format options
        Swal.fire({
            title: 'Export Format',
            text: 'Choose the export format:',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'CSV',
            cancelButtonText: 'Cancel',
            showDenyButton: true,
            denyButtonText: 'JSON',
            denyButtonColor: '#28a745'
        }).then((result) => {
            if (result.isConfirmed) {
                // Export as CSV
                exportSelectedRecords(selectedRecords, 'csv');
            } else if (result.isDenied) {
                // Export as JSON
                exportSelectedRecords(selectedRecords, 'json');
            }
        });
    }

    /**
     * Export selected records
     *
     * @param {Array} recordIds The record IDs to export
     * @param {string} format The export format (csv or json)
     */
    function exportSelectedRecords(recordIds, format) {
        // Show loading overlay
        $('.dab-loading-overlay').show();

        // Send AJAX request
        $.ajax({
            url: dab_data.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_export_selected_records',
                table_id: currentTableId,
                record_ids: recordIds,
                format: format,
                nonce: dab_data.nonce
            },
            success: function(response) {
                if (response.success) {
                    // For direct download, create a download link
                    if (response.data.download_url) {
                        const link = document.createElement('a');
                        link.href = response.data.download_url;
                        link.download = response.data.filename || `export.${format}`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    } else if (response.data.content) {
                        // For direct content, create a blob and download
                        const blob = new Blob([response.data.content], {
                            type: format === 'csv' ? 'text/csv' : 'application/json'
                        });
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = response.data.filename || `export.${format}`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                    }
                } else {
                    // Show error message
                    showError(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                showError(dab_data.i18n.error + ' (' + status + ': ' + error + ')');
            },
            complete: function() {
                // Hide loading overlay
                $('.dab-loading-overlay').hide();
            }
        });
    }
    /**
     * Get selected records
     *
     * @return {Array} Array of selected record IDs
     */
    function getSelectedRecords() {
        const selectedRecords = [];

        // Get all checked checkboxes
        $('#dab-data-table tbody input[type="checkbox"]:checked').each(function() {
            // Get the record ID from the row
            const recordId = $(this).closest('tr').find('.dab-edit-btn').data('id');
            if (recordId) {
                selectedRecords.push(recordId);
            }
        });

        return selectedRecords;
    }

    /**
     * Handle file upload for import
     *
     * @param {HTMLElement} fileInput The file input element
     */
    function handleFileUpload(fileInput) {
        const file = fileInput.files[0];
        if (!file) return;

        // Check file type
        const fileType = file.name.split('.').pop().toLowerCase();
        if (fileType !== 'csv' && fileType !== 'json') {
            showError('Invalid file type. Please upload a CSV or JSON file.');
            return;
        }

        // Show loading in preview
        $('.dab-preview-placeholder').text('Loading preview...');
        $('.dab-preview-table').hide();

        // Read file
        const reader = new FileReader();
        reader.onload = function(e) {
            const content = e.target.result;

            try {
                if (fileType === 'csv') {
                    previewCSV(content);
                } else {
                    previewJSON(content);
                }

                // Enable import button
                $('.dab-import-data').prop('disabled', false);
            } catch (error) {
                console.error('Error parsing file:', error);
                $('.dab-preview-placeholder').text('Error parsing file. Please check the file format.');
                $('.dab-preview-table').hide();
                $('.dab-import-data').prop('disabled', true);
            }
        };

        reader.onerror = function() {
            $('.dab-preview-placeholder').text('Error reading file.');
            $('.dab-preview-table').hide();
            $('.dab-import-data').prop('disabled', true);
        };

        if (fileType === 'csv') {
            reader.readAsText(file);
        } else {
            reader.readAsText(file);
        }
    }

    /**
     * Preview CSV data
     *
     * @param {string} content The CSV content
     */
    function previewCSV(content) {
        // Parse CSV
        const lines = content.split(/\r\n|\n/);
        const headers = lines[0].split(',');

        // Build table header
        let headerHtml = '<tr>';
        headers.forEach(function(header) {
            headerHtml += `<th>${header.replace(/"/g, '')}</th>`;
        });
        headerHtml += '</tr>';

        // Build table body (preview first 5 rows)
        let bodyHtml = '';
        const maxRows = Math.min(lines.length, 6);

        for (let i = 1; i < maxRows; i++) {
            if (lines[i].trim() === '') continue;

            const cells = parseCSVLine(lines[i]);
            bodyHtml += '<tr>';

            cells.forEach(function(cell) {
                bodyHtml += `<td>${cell.replace(/"/g, '')}</td>`;
            });

            bodyHtml += '</tr>';
        }

        // Update preview table
        $('.dab-preview-table thead').html(headerHtml);
        $('.dab-preview-table tbody').html(bodyHtml);
        $('.dab-preview-placeholder').hide();
        $('.dab-preview-table').show();
    }
    /**
     * Parse a CSV line respecting quoted values
     *
     * @param {string} line The CSV line to parse
     * @return {Array} Array of cell values
     */
    function parseCSVLine(line) {
        // Enhanced CSV parsing with better handling of quoted values
        const result = [];
        let current = '';
        let inQuotes = false;
        let previousChar = '';

        for (let i = 0; i < line.length; i++) {
            const char = line[i];

            // Handle escaped quotes (double quotes inside quoted values)
            if (char === '"' && previousChar === '"' && inQuotes) {
                // This is an escaped quote inside a quoted value
                current += '"';
                // Reset previousChar to avoid detecting another escaped quote
                previousChar = '';
            } else if (char === '"') {
                // Toggle quote state, but don't add the quote character
                if (inQuotes && i < line.length - 1 && line[i + 1] === '"') {
                    // This might be the start of an escaped quote, so just remember this char
                    previousChar = char;
                } else {
                    inQuotes = !inQuotes;
                    previousChar = char;
                }
            } else if (char === ',' && !inQuotes) {
                // End of field
                result.push(current.trim());
                current = '';
                previousChar = char;
            } else {
                // Regular character
                current += char;
                previousChar = char;
            }
        }

        // Add the last field
        result.push(current.trim());

        return result;
    }

    /**
     * Preview JSON data
     *
     * @param {string} content The JSON content
     */
    function previewJSON(content) {
        // Parse JSON
        const data = JSON.parse(content);

        if (!Array.isArray(data) || data.length === 0) {
            throw new Error('Invalid JSON format. Expected an array of objects.');
        }

        // Get headers from first object
        const headers = Object.keys(data[0]);

        // Build table header
        let headerHtml = '<tr>';
        headers.forEach(function(header) {
            headerHtml += `<th>${header}</th>`;
        });
        headerHtml += '</tr>';

        // Build table body (preview first 5 rows)
        let bodyHtml = '';
        const maxRows = Math.min(data.length, 5);

        for (let i = 0; i < maxRows; i++) {
            bodyHtml += '<tr>';

            headers.forEach(function(header) {
                const value = data[i][header];
                bodyHtml += `<td>${value !== null && value !== undefined ? value : ''}</td>`;
            });

            bodyHtml += '</tr>';
        }

        // Update preview table
        $('.dab-preview-table thead').html(headerHtml);
        $('.dab-preview-table tbody').html(bodyHtml);
        $('.dab-preview-placeholder').hide();
        $('.dab-preview-table').show();
    }

    /**
     * Import data
     */
    function importData() {
        console.log('Import data function called');
        const fileInput = document.getElementById('dab-import-file');
        const file = fileInput.files[0];

        if (!file) {
            showError('Please select a file to import.');
            return;
        }

        // Get import options
        const overwrite = $('#dab-import-overwrite').is(':checked');
        const skipHeader = $('#dab-import-skip-header').is(':checked');

        console.log('Import options:', { overwrite, skipHeader });
        console.log('File to import:', file.name, 'Size:', file.size, 'Type:', file.type);

        // Create form data
        const formData = new FormData();
        formData.append('action', 'dab_import_data');
        formData.append('table_id', currentTableId);
        formData.append('file', file);
        formData.append('overwrite', overwrite ? '1' : '0');
        formData.append('skip_header', skipHeader ? '1' : '0');
        formData.append('nonce', dab_data.nonce);

        // Show loading overlay
        $('.dab-loading-overlay').show();

        // Send AJAX request
        $.ajax({
            url: dab_data.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Import AJAX response:', response);
                if (response.success) {
                    // Log detailed response
                    console.log('Import successful with details:', response.data);

                    // Show success message
                    Swal.fire({
                        title: 'Import Successful',
                        text: response.data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        // Reset file input and preview
                        fileInput.value = '';
                        $('.dab-preview-placeholder').show();
                        $('.dab-preview-table').hide();
                        $('.dab-import-data').prop('disabled', true);

                        // Reload data with a slight delay to ensure the server has processed everything
                        setTimeout(() => {
                            console.log('Reloading table data after import');
                            console.log('Imported record IDs:', response.data.imported_ids);
                            console.log('Success count:', response.data.success_count);
                            console.log('Error count:', response.data.error_count);

                            // Completely destroy and recreate the DataTable
                            if (dataTable) {
                                try {
                                    dataTable.destroy();
                                    console.log('DataTable destroyed successfully');
                                } catch (e) {
                                    console.error('Error destroying DataTable:', e);
                                }
                                dataTable = null;
                            }

                            // Clear the table completely
                            $('#dab-data-table').empty();
                            $('#dab-data-table').html('<thead><tr></tr></thead><tbody></tbody>');
                            console.log('Table HTML reset');

                            // Force a refresh of the data
                            console.log('Calling loadTableData with table ID:', currentTableId);
                            loadTableData(currentTableId);
                        }, 1000);
                    });
                } else {
                    // Show error message
                    showError(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                showError(dab_data.i18n.error + ' (' + status + ': ' + error + ')');

                // Try to parse the response for more details
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    console.error('Parsed error response:', errorResponse);
                } catch (e) {
                    console.error('Could not parse error response');
                }
            },
            complete: function() {
                // Hide loading overlay
                $('.dab-loading-overlay').hide();
            }
        });
    }
    /**
     * Generate chart
     *
     * @param {string} chartType The chart type (bar, line, pie, etc.)
     * @param {string} xAxis The X-axis field
     * @param {string} yAxis The Y-axis field
     */
    function generateChart(chartType, xAxis, yAxis) {
        console.log('Generating chart:', chartType, 'X-axis:', xAxis, 'Y-axis:', yAxis);

        // Show loading overlay
        $('.dab-loading-overlay').show();

        // Clear any existing chart
        if (window.dataChart) {
            window.dataChart.destroy();
            window.dataChart = null;
        }

        // Fetch chart data via AJAX
        $.ajax({
            url: dab_data.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_get_dashboard_chart_data',
                table_id: currentTableId,
                chart_type: chartType,
                x_axis: xAxis,
                y_axis: yAxis,
                filters: activeFilters,
                nonce: dab_data.nonce
            },
            success: function(response) {
                console.log('Chart data response:', response);

                if (response.success) {
                    renderChart(chartType, response.data, xAxis, yAxis);
                } else {
                    showError(response.data || 'Failed to get chart data');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                showError(dab_data.i18n.error + ' (' + status + ': ' + error + ')');
            },
            complete: function() {
                // Hide loading overlay
                $('.dab-loading-overlay').hide();
            }
        });
    }

    /**
     * Render chart
     *
     * @param {string} chartType The chart type
     * @param {Object} data The chart data
     * @param {string} xAxisField The X-axis field name
     * @param {string} yAxisField The Y-axis field name
     */
    function renderChart(chartType, data, xAxisField, yAxisField) {


        // Get chart container and make it visible
        const chartContainer = $('.dab-chart-container');
        chartContainer.show();

        // Get canvas element
        const canvas = document.getElementById('dab-data-chart');
        const ctx = canvas.getContext('2d');

        // Clear any existing chart
        if (window.dataChart) {
            window.dataChart.destroy();
        }

        // Get field labels for better display
        const xAxisLabel = getFieldLabel(xAxisField);
        const yAxisLabel = getFieldLabel(yAxisField);

        // Prepare chart data
        const labels = data.labels || [];
        const values = data.values || [];

        // Generate colors for datasets
        const colors = generateChartColors(labels.length);

        // Configure chart options based on chart type
        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: ['pie', 'doughnut'].includes(chartType),
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            let value = context.raw || 0;

                            if (['pie', 'doughnut'].includes(chartType)) {
                                const total = values.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            } else {
                                return `${label}: ${value}`;
                            }
                        }
                    }
                },
                title: {
                    display: true,
                    text: `${yAxisLabel} by ${xAxisLabel}`,
                    font: {
                        size: 16
                    }
                }
            }
        };

        // Add scales for bar and line charts
        if (['bar', 'line'].includes(chartType)) {
            chartOptions.scales = {
                x: {
                    title: {
                        display: true,
                        text: xAxisLabel
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: yAxisLabel
                    },
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            // Format large numbers with K, M, etc.
                            if (value >= 1000000) {
                                return (value / 1000000).toFixed(1) + 'M';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(1) + 'K';
                            }
                            return value;
                        }
                    }
                }
            };
        }

        // Create dataset configuration based on chart type
        let datasets = [];

        if (['pie', 'doughnut'].includes(chartType)) {
            datasets = [{
                data: values,
                backgroundColor: colors,
                borderColor: 'white',
                borderWidth: 1
            }];

            // Add cutout for doughnut chart
            if (chartType === 'doughnut') {
                chartOptions.cutout = '50%';
            }
        } else {
            // For bar and line charts
            datasets = [{
                label: yAxisLabel,
                data: values,
                backgroundColor: chartType === 'bar' ? colors : 'rgba(54, 162, 235, 0.5)',
                borderColor: chartType === 'line' ? 'rgba(54, 162, 235, 1)' : 'white',
                borderWidth: 1,
                tension: chartType === 'line' ? 0.4 : 0 // Add curve to line charts
            }];
        }

        // Create the chart
        window.dataChart = new Chart(ctx, {
            type: chartType,
            data: {
                labels: labels,
                datasets: datasets
            },
            options: chartOptions
        });

        // Scroll to chart
        $('html, body').animate({
            scrollTop: chartContainer.offset().top - 100
        }, 500);
    }

    /**
     * Get field label from field slug
     *
     * @param {string} fieldSlug The field slug
     * @return {string} The field label
     */
    function getFieldLabel(fieldSlug) {
        const field = tableFields.find(f => f.field_slug === fieldSlug);
        return field ? field.field_label : fieldSlug;
    }

    /**
     * Generate chart colors
     *
     * @param {number} count Number of colors to generate
     * @return {Array} Array of colors
     */
    function generateChartColors(count) {
        const baseColors = [
            'rgba(54, 162, 235, 0.8)',   // Blue
            'rgba(255, 99, 132, 0.8)',   // Red
            'rgba(75, 192, 192, 0.8)',   // Green
            'rgba(255, 159, 64, 0.8)',   // Orange
            'rgba(153, 102, 255, 0.8)',  // Purple
            'rgba(255, 205, 86, 0.8)',   // Yellow
            'rgba(201, 203, 207, 0.8)'   // Grey
        ];

        // If we need more colors than in our base set, generate them
        if (count <= baseColors.length) {
            return baseColors.slice(0, count);
        }

        const colors = [...baseColors];

        // Generate additional colors
        for (let i = baseColors.length; i < count; i++) {
            const r = Math.floor(Math.random() * 255);
            const g = Math.floor(Math.random() * 255);
            const b = Math.floor(Math.random() * 255);
            colors.push(`rgba(${r}, ${g}, ${b}, 0.8)`);
        }

        return colors;
    }

    /**
     * Add a filter
     *
     * @param {string} field The field to filter on
     * @param {string} operator The filter operator
     * @param {string} value The filter value
     * @param {string} value2 The second filter value (for 'between' operator)
     */
    function addFilter(field, operator, value, value2) {
        console.log('Adding filter:', field, operator, value, value2);

        // Get field label
        const fieldObj = tableFields.find(f => f.field_slug === field);
        const fieldLabel = fieldObj ? fieldObj.field_label : field;

        // Get operator label
        const operatorLabels = {
            'equals': 'equals',
            'not_equals': 'does not equal',
            'contains': 'contains',
            'not_contains': 'does not contain',
            'starts_with': 'starts with',
            'ends_with': 'ends with',
            'greater_than': 'is greater than',
            'less_than': 'is less than',
            'greater_than_equal': 'is greater than or equal to',
            'less_than_equal': 'is less than or equal to',
            'between': 'is between',
            'is_empty': 'is empty',
            'is_not_empty': 'is not empty'
        };

        const operatorLabel = operatorLabels[operator] || operator;

        // Create filter object
        const filter = {
            field: field,
            operator: operator,
            value: value
        };

        // Add second value for 'between' operator
        if (operator === 'between' && value2) {
            filter.value2 = value2;
        }

        // Add to active filters
        activeFilters.push(filter);

        // Create filter tag
        let filterText = `${fieldLabel} ${operatorLabel}`;
        if (value && !['is_empty', 'is_not_empty'].includes(operator)) {
            filterText += ` "${value}"`;
            if (operator === 'between' && value2) {
                filterText += ` and "${value2}"`;
            }
        }

        const filterTag = $(`
            <div class="dab-filter-tag" data-index="${activeFilters.length - 1}">
                <span class="dab-filter-text">${filterText}</span>
                <button type="button" class="dab-remove-filter">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
        `);

        // Add to filter tags container
        $('.dab-filter-tags').append(filterTag);

        // Add event listener for remove button
        filterTag.find('.dab-remove-filter').on('click', function() {
            const index = $(this).closest('.dab-filter-tag').data('index');
            removeFilter(index);
        });

        // Clear filter inputs
        $('#dab-filter-field').val('');
        $('#dab-filter-operator').val('');
        $('#dab-filter-value').val('');
        $('#dab-filter-value2').val('');

        // Hide second value field
        $('.dab-filter-value2-container').hide();

        // Show active filters section
        $('.dab-active-filters').show();

        // Apply filters
        applyFilters();
    }

    /**
     * Remove a filter
     *
     * @param {number} index The filter index to remove
     */
    function removeFilter(index) {
        console.log('Removing filter at index:', index);

        // Remove from active filters
        activeFilters.splice(index, 1);

        // Remove filter tag
        $(`.dab-filter-tag[data-index="${index}"]`).remove();

        // Update remaining filter tags' indices
        $('.dab-filter-tag').each(function(i) {
            $(this).attr('data-index', i);
        });

        // Hide active filters section if no filters
        if (activeFilters.length === 0) {
            $('.dab-active-filters').hide();
        }

        // Apply filters
        applyFilters();
    }

    /**
     * Clear all filters
     */
    function clearAllFilters() {
        console.log('Clearing all filters');

        // Clear active filters
        activeFilters = [];

        // Clear filter tags
        $('.dab-filter-tags').empty();

        // Hide active filters section
        $('.dab-active-filters').hide();

        // Apply filters (reload data)
        applyFilters();

        // Show success message
        Swal.fire({
            title: 'Filters Cleared',
            text: 'All filters have been cleared.',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
        });
    }

    /**
     * Apply filters
     */
    function applyFilters() {
        console.log('Applying filters:', activeFilters);

        // Reload table data with filters
        loadTableData(currentTableId);
    }

    /**
     * Add a filter condition to the advanced filter builder
     *
     * @param {jQuery} container The container to add the condition to
     */
    function addFilterCondition(container) {
        console.log('Adding filter condition to container:', container);

        // Create filter condition HTML
        const conditionHtml = `
            <div class="dab-filter-condition">
                <select class="dab-condition-field">
                    <option value="">Select Field</option>
                    ${tableFields.map(field => `<option value="${field.field_slug}">${field.field_label}</option>`).join('')}
                </select>
                <select class="dab-condition-operator">
                    <option value="equals">Equals</option>
                    <option value="not_equals">Does Not Equal</option>
                    <option value="contains">Contains</option>
                    <option value="not_contains">Does Not Contain</option>
                    <option value="starts_with">Starts With</option>
                    <option value="ends_with">Ends With</option>
                    <option value="greater_than">Greater Than</option>
                    <option value="less_than">Less Than</option>
                    <option value="greater_than_equal">Greater Than or Equal</option>
                    <option value="less_than_equal">Less Than or Equal</option>
                    <option value="between">Between</option>
                    <option value="is_empty">Is Empty</option>
                    <option value="is_not_empty">Is Not Empty</option>
                </select>
                <div class="dab-condition-value-container">
                    <input type="text" class="dab-condition-value" placeholder="Value">
                </div>
                <div class="dab-condition-value2-container" style="display: none;">
                    <input type="text" class="dab-condition-value2" placeholder="Second Value">
                </div>
                <button type="button" class="dab-remove-condition">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
        `;

        // Add to container
        const condition = $(conditionHtml);
        container.append(condition);

        // Add event listener for remove button
        condition.find('.dab-remove-condition').on('click', function() {
            $(this).closest('.dab-filter-condition').remove();
        });

        // Add event listener for operator change
        condition.find('.dab-condition-operator').on('change', function() {
            const operator = $(this).val();
            const valueContainer = $(this).siblings('.dab-condition-value-container');
            const value2Container = $(this).siblings('.dab-condition-value2-container');

            if (operator === 'between') {
                value2Container.show();
            } else {
                value2Container.hide();
            }

            if (['is_empty', 'is_not_empty'].includes(operator)) {
                valueContainer.hide();
            } else {
                valueContainer.show();
            }
        });
    }

    /**
     * Add a filter group to the advanced filter builder
     */
    function addFilterGroup() {
        console.log('Adding filter group');

        // Create filter group HTML
        const groupHtml = `
            <div class="dab-filter-group-container">
                <div class="dab-filter-group-header">
                    <select class="dab-filter-group-operator">
                        <option value="AND">AND</option>
                        <option value="OR">OR</option>
                    </select>
                    <button type="button" class="dab-add-filter-condition">
                        <span class="dashicons dashicons-plus"></span> Add Condition
                    </button>
                    <button type="button" class="dab-remove-filter-group">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>
                <div class="dab-filter-conditions">
                    <!-- Conditions will be added dynamically -->
                </div>
            </div>
        `;

        // Add to filter builder
        const group = $(groupHtml);
        $('.dab-advanced-filter-builder').append(group);

        // Add event listener for add condition button
        group.find('.dab-add-filter-condition').on('click', function() {
            addFilterCondition($(this).closest('.dab-filter-group-container').find('.dab-filter-conditions'));
        });

        // Add event listener for remove group button
        group.find('.dab-remove-filter-group').on('click', function() {
            $(this).closest('.dab-filter-group-container').remove();
        });

        // Add initial condition
        addFilterCondition(group.find('.dab-filter-conditions'));
    }

    /**
     * Apply advanced filters
     */
    function applyAdvancedFilters() {
        console.log('Applying advanced filters');

        // Clear existing filters
        activeFilters = [];

        // Process each filter group
        $('.dab-filter-group-container').each(function() {
            const groupOperator = $(this).find('.dab-filter-group-operator').val();
            const conditions = [];

            // Process each condition in this group
            $(this).find('.dab-filter-condition').each(function() {
                const field = $(this).find('.dab-condition-field').val();
                const operator = $(this).find('.dab-condition-operator').val();
                const value = $(this).find('.dab-condition-value').val();
                const value2 = $(this).find('.dab-condition-value2').val();

                if (field && operator) {
                    const condition = {
                        field: field,
                        operator: operator,
                        value: value
                    };

                    if (operator === 'between' && value2) {
                        condition.value2 = value2;
                    }

                    conditions.push(condition);
                }
            });

            if (conditions.length > 0) {
                // Add group to active filters
                activeFilters.push({
                    type: 'group',
                    operator: groupOperator,
                    conditions: conditions
                });
            }
        });

        // Update filter tags
        updateFilterTags();

        // Apply filters
        applyFilters();
    }

    /**
     * Save advanced filters
     */
    function saveAdvancedFilters() {
        console.log('Saving advanced filters');

        // First apply the filters to make sure they're valid
        applyAdvancedFilters();

        // Prompt for filter set name
        Swal.fire({
            title: 'Save Filter Set',
            input: 'text',
            inputLabel: 'Filter Set Name',
            inputPlaceholder: 'Enter a name for this filter set',
            showCancelButton: true,
            inputValidator: (value) => {
                if (!value) {
                    return 'You need to enter a name!';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const filterSetName = result.value;

                // Get existing saved filters
                let savedFilters = localStorage.getItem('dab_saved_filters');
                savedFilters = savedFilters ? JSON.parse(savedFilters) : {};

                // Add table-specific filters if not exists
                if (!savedFilters[currentTableId]) {
                    savedFilters[currentTableId] = [];
                }

                // Add new filter set
                savedFilters[currentTableId].push({
                    name: filterSetName,
                    filters: activeFilters
                });

                // Save to localStorage
                localStorage.setItem('dab_saved_filters', JSON.stringify(savedFilters));

                // Update saved filters list
                updateSavedFiltersList();

                // Show success message
                Swal.fire({
                    title: 'Saved!',
                    text: 'Filter set has been saved.',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    }

    /**
     * Update saved filters list
     */
    function updateSavedFiltersList() {
        console.log('Updating saved filters list');

        // Get saved filters
        let savedFilters = localStorage.getItem('dab_saved_filters');
        savedFilters = savedFilters ? JSON.parse(savedFilters) : {};

        // Get filters for current table
        const tableFilters = savedFilters[currentTableId] || [];

        // Update UI
        const container = $('.dab-saved-filters-list');
        container.empty();

        if (tableFilters.length === 0) {
            container.html('<p class="dab-no-saved-filters">No saved filters yet. Create and save filters to see them here.</p>');
        } else {
            // Create list of saved filters
            tableFilters.forEach((filterSet, index) => {
                const filterItem = $(`
                    <div class="dab-saved-filter-item" data-index="${index}">
                        <div class="dab-saved-filter-name">${filterSet.name}</div>
                        <div class="dab-saved-filter-actions">
                            <button type="button" class="dab-apply-saved-filter" title="Apply Filter">
                                <span class="dashicons dashicons-yes"></span>
                            </button>
                            <button type="button" class="dab-delete-saved-filter" title="Delete Filter">
                                <span class="dashicons dashicons-trash"></span>
                            </button>
                        </div>
                    </div>
                `);

                container.append(filterItem);
            });

            // Add event listeners
            $('.dab-apply-saved-filter').on('click', function() {
                const index = $(this).closest('.dab-saved-filter-item').data('index');
                applySavedFilter(index);
            });

            $('.dab-delete-saved-filter').on('click', function() {
                const index = $(this).closest('.dab-saved-filter-item').data('index');
                deleteSavedFilter(index);
            });
        }
    }

    /**
     * Apply a saved filter
     *
     * @param {number} index The index of the saved filter to apply
     */
    function applySavedFilter(index) {
        console.log('Applying saved filter at index:', index);

        // Get saved filters
        let savedFilters = localStorage.getItem('dab_saved_filters');
        savedFilters = savedFilters ? JSON.parse(savedFilters) : {};

        // Get filters for current table
        const tableFilters = savedFilters[currentTableId] || [];

        if (tableFilters[index]) {
            // Set active filters
            activeFilters = tableFilters[index].filters;

            // Update filter tags
            updateFilterTags();

            // Apply filters
            applyFilters();

            // Show success message
            Swal.fire({
                title: 'Filter Applied',
                text: `Applied filter set: ${tableFilters[index].name}`,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
    }

    /**
     * Delete a saved filter
     *
     * @param {number} index The index of the saved filter to delete
     */
    function deleteSavedFilter(index) {
        console.log('Deleting saved filter at index:', index);

        // Confirm deletion
        Swal.fire({
            title: 'Delete Filter Set',
            text: 'Are you sure you want to delete this filter set?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                // Get saved filters
                let savedFilters = localStorage.getItem('dab_saved_filters');
                savedFilters = savedFilters ? JSON.parse(savedFilters) : {};

                // Get filters for current table
                const tableFilters = savedFilters[currentTableId] || [];

                if (tableFilters[index]) {
                    // Remove filter set
                    tableFilters.splice(index, 1);

                    // Update saved filters
                    savedFilters[currentTableId] = tableFilters;
                    localStorage.setItem('dab_saved_filters', JSON.stringify(savedFilters));

                    // Update saved filters list
                    updateSavedFiltersList();

                    // Show success message
                    Swal.fire({
                        title: 'Deleted!',
                        text: 'Filter set has been deleted.',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
            }
        });
    }

    /**
     * Update filter tags
     */
    function updateFilterTags() {
        console.log('Updating filter tags');

        // Clear filter tags
        $('.dab-filter-tags').empty();

        // Add tag for each filter
        activeFilters.forEach((filter, index) => {
            if (filter.type === 'group') {
                // Group filter
                const groupTag = $(`
                    <div class="dab-filter-tag dab-filter-group-tag" data-index="${index}">
                        <span class="dab-filter-text">Filter Group (${filter.operator})</span>
                        <button type="button" class="dab-remove-filter">
                            <span class="dashicons dashicons-no-alt"></span>
                        </button>
                    </div>
                `);

                $('.dab-filter-tags').append(groupTag);
            } else {
                // Simple filter
                const fieldObj = tableFields.find(f => f.field_slug === filter.field);
                const fieldLabel = fieldObj ? fieldObj.field_label : filter.field;

                const operatorLabels = {
                    'equals': 'equals',
                    'not_equals': 'does not equal',
                    'contains': 'contains',
                    'not_contains': 'does not contain',
                    'starts_with': 'starts with',
                    'ends_with': 'ends with',
                    'greater_than': 'is greater than',
                    'less_than': 'is less than',
                    'greater_than_equal': 'is greater than or equal to',
                    'less_than_equal': 'is less than or equal to',
                    'between': 'is between',
                    'is_empty': 'is empty',
                    'is_not_empty': 'is not empty'
                };

                const operatorLabel = operatorLabels[filter.operator] || filter.operator;

                let filterText = `${fieldLabel} ${operatorLabel}`;
                if (filter.value && !['is_empty', 'is_not_empty'].includes(filter.operator)) {
                    filterText += ` "${filter.value}"`;
                    if (filter.operator === 'between' && filter.value2) {
                        filterText += ` and "${filter.value2}"`;
                    }
                }

                const filterTag = $(`
                    <div class="dab-filter-tag" data-index="${index}">
                        <span class="dab-filter-text">${filterText}</span>
                        <button type="button" class="dab-remove-filter">
                            <span class="dashicons dashicons-no-alt"></span>
                        </button>
                    </div>
                `);

                $('.dab-filter-tags').append(filterTag);
            }
        });

        // Add event listeners for remove buttons
        $('.dab-remove-filter').on('click', function() {
            const index = $(this).closest('.dab-filter-tag').data('index');
            removeFilter(index);
        });

        // Show/hide active filters section
        if (activeFilters.length > 0) {
            $('.dab-active-filters').show();
        } else {
            $('.dab-active-filters').hide();
        }
    }

    /**
     * Export all data
     */
    function exportData() {
        // Show export format options
        Swal.fire({
            title: 'Export Format',
            text: 'Choose the export format:',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'CSV',
            cancelButtonText: 'Cancel',
            showDenyButton: true,
            denyButtonText: 'JSON',
            denyButtonColor: '#28a745'
        }).then((result) => {
            if (result.isConfirmed) {
                // Export as CSV
                window.location.href = `${dab_data.ajax_url}?action=dab_export_data&table_id=${currentTableId}&format=csv&nonce=${dab_data.nonce}`;
            } else if (result.isDenied) {
                // Export as JSON
                window.location.href = `${dab_data.ajax_url}?action=dab_export_data&table_id=${currentTableId}&format=json&nonce=${dab_data.nonce}`;
            }
        });
    }

    /**
     * Show error
     *
     * @param {string} message The error message
     */
    function showError(message) {
        Swal.fire({
            title: 'Error!',
            text: message,
            icon: 'error',
            confirmButtonText: 'OK'
        });
    }

    // Initialize dashboard when document is ready
    $(document).ready(function() {
        if ($('.dab-data-dashboard-wrap').length) {
            console.log('Initializing Data Management Dashboard...');
            console.log('AJAX URL:', dab_data.ajax_url);
            console.log('Nonce:', dab_data.nonce);
            initDashboard();
        }
    });
})(jQuery);
