/**
 * Simple Dashboard Builder Styles
 */

/* Dashboard Builder Container */
.dab-dashboard-builder-wrap {
    margin: 20px 0;
}

.dab-dashboard-builder-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

/* Sidebar */
.dab-dashboard-builder-sidebar {
    flex: 0 0 300px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dab-dashboard-builder-settings,
.dab-dashboard-builder-widgets,
.dab-dashboard-builder-help {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.dab-dashboard-builder-help {
    border-bottom: none;
}

.dab-form-group {
    margin-bottom: 15px;
}

.dab-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.dab-form-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

/* Available Widgets */
.dab-widget-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s ease;
}

.dab-widget-item:hover {
    background-color: #f0f0f0;
    border-color: #ccc;
}

.dab-widget-icon {
    margin-right: 10px;
    font-size: 20px;
    color: #555;
}

.dab-widget-label {
    font-weight: 500;
}

/* Canvas */
.dab-dashboard-builder-canvas {
    flex: 1;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    min-height: 600px;
    overflow: auto;
}

.dab-dashboard-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-height: 600px;
    position: relative;
    padding: 10px;
}

/* Row Styles */
.dab-dashboard-row {
    display: flex;
    gap: 15px;
    min-height: 100px;
    width: 100%;
    position: relative;
    background-color: rgba(240, 240, 240, 0.5);
    border-radius: 5px;
    padding: 10px;
    border: 1px dashed #ccc;
    box-sizing: border-box;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.dab-dashboard-row:hover {
    background-color: rgba(230, 230, 230, 0.7);
}

/* Column Styles */
.dab-dashboard-column {
    flex: 1 1 0;
    min-height: 100px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    padding: 10px;
    border: 1px dashed #ddd;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 15px;
    transition: all 0.3s ease;
    box-sizing: border-box;
    min-width: 100px;
    overflow: visible;
}

.dab-dashboard-column:hover {
    background-color: rgba(250, 250, 250, 0.9);
}

/* Column width indicator */
.dab-column-width {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background-color: rgba(240, 240, 240, 0.8);
    border-radius: 3px;
    padding: 2px 5px;
    font-size: 10px;
    color: #555;
    z-index: 5;
}

/* Row and Column Controls */
.dab-row-controls, .dab-column-controls {
    position: absolute;
    display: flex;
    gap: 5px;
    z-index: 100; /* Increased z-index to ensure controls are always on top */
}

.dab-row-controls {
    top: -15px;
    right: 10px;
    background-color: #f0f0f0;
    border-radius: 3px;
    padding: 3px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border: 1px solid #ddd;
}

.dab-column-controls {
    top: -10px;
    right: 5px;
    background-color: #f9f9f9;
    border-radius: 3px;
    padding: 2px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border: 1px solid #ddd;
}

.dab-row-controls button, .dab-column-controls button {
    background: none;
    border: none;
    cursor: pointer;
    color: #555;
    padding: 0;
    width: 28px; /* Increased size for better clickability */
    height: 28px; /* Increased size for better clickability */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    position: relative; /* Added for better positioning */
}

.dab-row-controls button:hover, .dab-column-controls button:hover {
    background-color: #e0e0e0;
    color: #0073aa;
    transform: scale(1.1); /* Slight scale effect on hover */
}

/* Add focus styles for better accessibility */
.dab-row-controls button:focus, .dab-column-controls button:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Make controls more visible on row/column hover */
.dab-dashboard-row:hover .dab-row-controls,
.dab-dashboard-column:hover .dab-column-controls {
    opacity: 1;
    visibility: visible;
}

/* Ensure the controls are always visible but slightly transparent when not hovered */
.dab-row-controls, .dab-column-controls {
    opacity: 0.8;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Add Row Button */
.dab-add-row-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    margin-bottom: 20px;
    width: 100%;
}

.dab-add-row-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    background-color: #f0f0f0;
    border: 1px dashed #ccc;
    border-radius: 4px;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 50%;
    max-width: 300px;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dab-add-row-btn:hover {
    background-color: #e0e0e0;
    color: #0073aa;
    border-color: #0073aa;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Empty column placeholder */
.dab-empty-column-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    color: #888;
    font-style: italic;
    border: 2px dashed #ddd;
    border-radius: 4px;
    margin: 10px 0;
    background-color: rgba(240, 240, 240, 0.3);
    transition: all 0.3s ease;
    padding: 20px;
}

.dab-empty-column-placeholder:hover {
    background-color: rgba(0, 115, 170, 0.05);
    border-color: #0073aa;
    color: #0073aa;
}

.dab-dashboard-column.column-drop-hover .dab-empty-column-placeholder {
    background-color: rgba(0, 115, 170, 0.1);
    border-color: #0073aa;
    color: #0073aa;
    transform: scale(1.05);
}

.dab-empty-column-placeholder .dashicons {
    font-size: 24px;
    margin-bottom: 8px;
    color: inherit;
    transition: transform 0.3s ease;
}

.dab-empty-column-placeholder:hover .dashicons,
.dab-dashboard-column.column-drop-hover .dab-empty-column-placeholder .dashicons {
    transform: scale(1.2);
}

/* Column resizing handle */
.dab-column-resize-handle {
    position: absolute;
    top: 0;
    right: -8px;
    width: 16px;
    height: 100%;
    cursor: col-resize;
    z-index: 10;
    display: none;
}

.dab-dashboard-column:hover .dab-column-resize-handle {
    display: block;
}

.dab-column-resize-handle::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 30px;
    background-color: rgba(0, 115, 170, 0.3);
    border-radius: 2px;
}

/* Column Width Controls */
.dab-column-width {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background-color: rgba(240, 240, 240, 0.8);
    border-radius: 3px;
    padding: 2px 5px;
    font-size: 10px;
    color: #555;
}

/* Dashboard Widget */
.dab-dashboard-widget {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;
    min-height: 100px;
    cursor: move;
    margin-bottom: 10px;
    top: 0 !important;
    left: 0 !important;
}

.dab-widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f9f9f9;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.dab-widget-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.dab-widget-actions {
    display: flex;
    gap: 5px;
}

.dab-widget-actions button {
    background: none;
    border: none;
    cursor: pointer;
    color: #555;
    padding: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.dab-widget-actions button:hover {
    background-color: #f0f0f0;
    color: #0073aa;
}

.dab-widget-delete:hover {
    color: #d63638 !important;
}

.dab-widget-content {
    flex: 1;
    padding: 15px;
    overflow: auto;
}

.dab-widget-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #888;
}

.dab-widget-placeholder .dashicons {
    font-size: 24px;
    margin-bottom: 10px;
}

.dab-widget-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background-image: linear-gradient(135deg, transparent 50%, #ccc 50%, #ccc 60%, transparent 60%);
    background-size: 10px 10px;
    background-repeat: no-repeat;
    background-position: right bottom;
}

/* Modal */
.dab-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    overflow: auto;
}

.dab-modal-content {
    background-color: #fff;
    margin: 50px auto;
    padding: 0;
    width: 600px;
    max-width: 90%;
    border-radius: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.dab-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.dab-modal-header h2 {
    margin: 0;
    font-size: 18px;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #555;
}

.dab-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.dab-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Responsive */
@media (max-width: 1200px) {
    .dab-dashboard-builder-container {
        gap: 15px;
    }

    .dab-dashboard-builder-sidebar {
        flex: 0 0 280px;
    }

    .dab-dashboard-row {
        padding: 8px;
        gap: 10px;
    }

    .dab-dashboard-column {
        padding: 8px;
    }
}

@media (max-width: 992px) {
    .dab-dashboard-builder-container {
        flex-direction: column;
    }

    .dab-dashboard-builder-sidebar {
        flex: 0 0 auto;
        width: 100%;
    }

    /* Keep rows as flex containers that don't wrap by default */
    .dab-dashboard-row {
        display: flex;
        flex-wrap: nowrap; /* Don't wrap by default to match frontend */
        overflow-x: auto; /* Allow horizontal scrolling if needed */
        gap: 10px;
    }

    /* Columns should maintain their relative widths */
    .dab-dashboard-column {
        min-width: 200px; /* Ensure columns have a minimum width */
    }
}

@media (max-width: 768px) {
    /* On very small screens, allow wrapping for better mobile experience in the builder */
    .dab-dashboard-row {
        flex-wrap: wrap;
    }

    .dab-dashboard-column {
        flex: 1 1 100%;
        min-width: 100%;
    }

    /* Improve control visibility on mobile */
    .dab-row-controls, .dab-column-controls {
        top: -10px;
        right: 5px;
        padding: 3px;
        background-color: rgba(240, 240, 240, 0.95); /* More opaque background */
        border: 1px solid #ccc;
    }

    .dab-row-controls button, .dab-column-controls button {
        width: 30px; /* Larger buttons for touch screens */
        height: 30px; /* Larger buttons for touch screens */
    }

    .dab-dashboard-grid {
        padding: 5px;
    }

    /* Ensure controls are more visible on mobile */
    .dab-row-controls, .dab-column-controls {
        opacity: 1;
    }
}

/* Dragging States */
.dab-widget-item {
    cursor: grab;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dab-widget-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dab-widget-item.dragging {
    opacity: 0.7;
    transform: scale(1.02);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.dab-dashboard-widget {
    transition: opacity 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

.dab-dashboard-widget.dragging {
    opacity: 0.7;
    z-index: 1000 !important;
    cursor: grabbing !important;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    transform: scale(1.02);
    pointer-events: auto !important;
    position: absolute !important;
}

.dab-dashboard-grid.drag-over {
    background-color: #f0f0f0;
}

.dab-dashboard-column.column-drop-hover {
    background-color: rgba(0, 115, 170, 0.15);
    border: 2px dashed #0073aa;
    box-shadow: inset 0 0 10px rgba(0, 115, 170, 0.1);
    transition: all 0.3s ease;
}

.dab-dashboard-column.column-drop-active {
    background-color: rgba(0, 115, 170, 0.05);
}

/* Dashboard grid with active dragging */
.dab-dashboard-grid.dragging-in-progress {
    background-color: #f5f5f5;
}

/* Improve widget header for better dragging */
.dab-widget-header {
    cursor: move;
    cursor: grab;
}

.dab-widget-header:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dab-widget-header:active {
    cursor: grabbing;
}

/* Widget Settings Fields */
.dab-settings-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.dab-settings-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
}

.dab-field-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.dab-field-col {
    flex: 1;
}

/* Shortcode Info */
.dab-shortcode-info {
    margin-top: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
}

.dab-shortcode-info h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
}

.dab-shortcode-info code {
    display: block;
    padding: 8px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}
