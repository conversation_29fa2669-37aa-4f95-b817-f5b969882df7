<?php
/**
 * Dashboard Builder Page
 *
 * Admin page for building dashboards.
 */
if (!defined('ABSPATH')) exit;

// Check if we're editing an existing dashboard
$dashboard_id = isset($_GET['dashboard_id']) ? intval($_GET['dashboard_id']) : 0;
$dashboard = null;

if ($dashboard_id > 0) {
    $dashboard = DAB_Simple_Dashboard_Manager::get_dashboard($dashboard_id);
}

// Enqueue required scripts and styles
wp_enqueue_style('dab-simple-dashboard-builder', plugin_dir_url(dirname(__FILE__)) . 'assets/css/simple-dashboard-builder.css', array(), DAB_VERSION);
wp_enqueue_script('jquery-ui-draggable');
wp_enqueue_script('jquery-ui-droppable');
wp_enqueue_script('jquery-ui-resizable');
wp_enqueue_script('dab-simple-dashboard-builder', plugin_dir_url(dirname(__FILE__)) . 'assets/js/simple-dashboard-builder.js', array('jquery', 'jquery-ui-draggable', 'jquery-ui-droppable', 'jquery-ui-resizable'), DAB_VERSION, true);

// Get all tables for widget configuration
global $wpdb;
$tables_table = $wpdb->prefix . 'dab_tables';
$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label ASC");

// Localize script with necessary variables
wp_localize_script('dab-simple-dashboard-builder', 'dab_dashboard', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('dab_dashboard_nonce'),
    'dashboard_id' => $dashboard_id,
    'dashboard' => $dashboard,
    'tables' => $tables,
    'i18n' => array(
        'save_success' => __('Dashboard saved successfully.', 'db-app-builder'),
        'save_error' => __('Failed to save dashboard.', 'db-app-builder'),
        'delete_confirm' => __('Are you sure you want to delete this dashboard? This action cannot be undone.', 'db-app-builder'),
        'delete_widget_confirm' => __('Are you sure you want to delete this widget? This action cannot be undone.', 'db-app-builder'),
        'widget_save_success' => __('Widget saved successfully.', 'db-app-builder'),
        'widget_save_error' => __('Failed to save widget.', 'db-app-builder'),
        'widget_delete_success' => __('Widget deleted successfully.', 'db-app-builder'),
        'widget_delete_error' => __('Failed to delete widget.', 'db-app-builder'),
    )
));
?>

<div class="wrap dab-dashboard-builder-wrap">
    <h1 class="wp-heading-inline">
        <?php echo $dashboard_id > 0 ? __('Edit Dashboard', 'db-app-builder') : __('Create Dashboard', 'db-app-builder'); ?>
    </h1>
    <a href="<?php echo admin_url('admin.php?page=dab_dashboards'); ?>" class="page-title-action"><?php _e('Back to Dashboards', 'db-app-builder'); ?></a>

    <hr class="wp-header-end">

    <div class="dab-dashboard-builder-container">
        <div class="dab-dashboard-builder-sidebar">
            <div class="dab-dashboard-builder-settings">
                <h2><?php _e('Dashboard Settings', 'db-app-builder'); ?></h2>

                <div class="dab-form-group">
                    <label for="dashboard-title"><?php _e('Title', 'db-app-builder'); ?></label>
                    <input type="text" id="dashboard-title" class="regular-text" value="<?php echo $dashboard ? esc_attr($dashboard->title) : ''; ?>" required>
                </div>

                <div class="dab-form-group">
                    <label for="dashboard-description"><?php _e('Description', 'db-app-builder'); ?></label>
                    <textarea id="dashboard-description" class="large-text" rows="3"><?php echo $dashboard ? esc_textarea($dashboard->description) : ''; ?></textarea>
                </div>

                <div class="dab-form-group">
                    <label>
                        <input type="checkbox" id="dashboard-is-public" <?php echo $dashboard && $dashboard->is_public ? 'checked' : ''; ?>>
                        <?php _e('Make this dashboard public', 'db-app-builder'); ?>
                    </label>
                    <p class="description"><?php _e('Public dashboards can be embedded using shortcodes.', 'db-app-builder'); ?></p>
                </div>

                <div class="dab-form-actions">
                    <button type="button" id="save-dashboard" class="button button-primary"><?php _e('Save Dashboard', 'db-app-builder'); ?></button>
                    <button type="button" id="dashboard-permissions" class="button"><?php _e('Permissions', 'db-app-builder'); ?></button>

                    <?php if ($dashboard_id > 0): ?>
                    <a href="<?php echo admin_url('admin.php?page=dab_dashboard_view&dashboard_id=' . $dashboard_id); ?>" class="button" target="_blank"><?php _e('Preview', 'db-app-builder'); ?></a>
                    <button type="button" id="delete-dashboard" class="button button-link-delete"><?php _e('Delete', 'db-app-builder'); ?></button>
                    <?php endif; ?>
                </div>
            </div>

            <div class="dab-dashboard-builder-widgets">
                <h2><?php _e('Available Widgets', 'db-app-builder'); ?></h2>

                <div class="dab-widget-item" data-widget-type="table" draggable="true">
                    <div class="dab-widget-icon dashicons dashicons-list-view"></div>
                    <div class="dab-widget-label"><?php _e('Table', 'db-app-builder'); ?></div>
                </div>

                <div class="dab-widget-item" data-widget-type="chart" draggable="true">
                    <div class="dab-widget-icon dashicons dashicons-chart-bar"></div>
                    <div class="dab-widget-label"><?php _e('Chart', 'db-app-builder'); ?></div>
                </div>

                <div class="dab-widget-item" data-widget-type="metric" draggable="true">
                    <div class="dab-widget-icon dashicons dashicons-dashboard"></div>
                    <div class="dab-widget-label"><?php _e('Metric', 'db-app-builder'); ?></div>
                </div>

                <div class="dab-widget-item" data-widget-type="text" draggable="true">
                    <div class="dab-widget-icon dashicons dashicons-text"></div>
                    <div class="dab-widget-label"><?php _e('Text', 'db-app-builder'); ?></div>
                </div>

                <div class="dab-widget-item" data-widget-type="kpi" draggable="true">
                    <div class="dab-widget-icon dashicons dashicons-chart-line"></div>
                    <div class="dab-widget-label"><?php _e('KPI', 'db-app-builder'); ?></div>
                </div>

                <div class="dab-widget-item" data-widget-type="list" draggable="true">
                    <div class="dab-widget-icon dashicons dashicons-menu"></div>
                    <div class="dab-widget-label"><?php _e('List', 'db-app-builder'); ?></div>
                </div>

                <div class="dab-widget-item" data-widget-type="progress" draggable="true">
                    <div class="dab-widget-icon dashicons dashicons-performance"></div>
                    <div class="dab-widget-label"><?php _e('Progress', 'db-app-builder'); ?></div>
                </div>

                <div class="dab-widget-item" data-widget-type="image" draggable="true">
                    <div class="dab-widget-icon dashicons dashicons-format-image"></div>
                    <div class="dab-widget-label"><?php _e('Image', 'db-app-builder'); ?></div>
                </div>
            </div>

            <div class="dab-dashboard-builder-help">
                <h2><?php _e('Help', 'db-app-builder'); ?></h2>

                <p><?php _e('Drag and drop widgets from the sidebar to the canvas.', 'db-app-builder'); ?></p>
                <p><?php _e('Click on a widget to edit its settings.', 'db-app-builder'); ?></p>
                <p><?php _e('Resize widgets by dragging the bottom-right corner.', 'db-app-builder'); ?></p>
                <p><?php _e('Save your dashboard when you\'re done.', 'db-app-builder'); ?></p>

                <?php if ($dashboard_id > 0 && $dashboard && $dashboard->is_public): ?>
                <div class="dab-shortcode-info">
                    <h3><?php _e('Shortcode', 'db-app-builder'); ?></h3>
                    <code>[dab_dashboard id="<?php echo $dashboard_id; ?>"]</code>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="dab-dashboard-builder-canvas">
            <div class="dab-dashboard-grid" id="dashboard-grid">
                <?php
                // Check if we have a layout defined
                $layout = null;
                if ($dashboard && isset($dashboard->layout)) {
                    $layout = json_decode($dashboard->layout, true);
                }

                if ($layout && !empty($layout['rows'])):
                    // Render rows and columns from layout
                    foreach ($layout['rows'] as $row_index => $row):
                ?>
                    <div class="dab-dashboard-row" data-row-id="<?php echo $row_index; ?>">
                        <div class="dab-row-controls">
                            <button type="button" class="dab-row-add-column" title="<?php _e('Add Column', 'db-app-builder'); ?>">
                                <span class="dashicons dashicons-plus"></span>
                            </button>
                            <button type="button" class="dab-row-settings" title="<?php _e('Row Settings', 'db-app-builder'); ?>">
                                <span class="dashicons dashicons-admin-generic"></span>
                            </button>
                            <button type="button" class="dab-row-delete" title="<?php _e('Delete Row', 'db-app-builder'); ?>">
                                <span class="dashicons dashicons-trash"></span>
                            </button>
                        </div>

                        <?php foreach ($row['columns'] as $col_index => $column): ?>
                            <div class="dab-dashboard-column"
                                 data-column-id="<?php echo $col_index; ?>"
                                 data-width="<?php echo isset($column['width']) ? $column['width'] : 1; ?>"
                                 style="flex: <?php echo isset($column['width']) ? $column['width'] : 1; ?>">

                                <div class="dab-column-controls">
                                    <button type="button" class="dab-column-settings" title="<?php _e('Column Settings', 'db-app-builder'); ?>">
                                        <span class="dashicons dashicons-admin-generic"></span>
                                    </button>
                                    <button type="button" class="dab-column-delete" title="<?php _e('Delete Column', 'db-app-builder'); ?>">
                                        <span class="dashicons dashicons-trash"></span>
                                    </button>
                                </div>

                                <div class="dab-column-width">
                                    <?php echo isset($column['width']) ? $column['width'] : 1; ?>
                                </div>

                                <?php
                                // Render widgets in this column
                                if (!empty($column['widgets'])) {
                                    foreach ($column['widgets'] as $widget_id) {
                                        // Find the widget in the dashboard widgets
                                        $widget = null;
                                        if ($dashboard && !empty($dashboard->widgets)) {
                                            foreach ($dashboard->widgets as $w) {
                                                if ($w->id == $widget_id) {
                                                    $widget = $w;
                                                    break;
                                                }
                                            }
                                        }

                                        if ($widget) {
                                            $settings = json_decode($widget->settings, true);
                                ?>
                                            <div class="dab-dashboard-widget"
                                                 data-widget-id="<?php echo $widget->id; ?>"
                                                 data-widget-type="<?php echo $widget->widget_type; ?>"
                                                 data-row-id="<?php echo $row_index; ?>"
                                                 data-column-id="<?php echo $col_index; ?>"
                                                 data-width="<?php echo $widget->width; ?>"
                                                 data-height="<?php echo $widget->height; ?>">
                                                <div class="dab-widget-header">
                                                    <h3 class="dab-widget-title"><?php echo esc_html($widget->title); ?></h3>
                                                    <div class="dab-widget-actions">
                                                        <button type="button" class="dab-widget-edit" title="<?php _e('Edit Widget', 'db-app-builder'); ?>">
                                                            <span class="dashicons dashicons-edit"></span>
                                                        </button>
                                                        <button type="button" class="dab-widget-delete" title="<?php _e('Delete Widget', 'db-app-builder'); ?>">
                                                            <span class="dashicons dashicons-trash"></span>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="dab-widget-content">
                                                    <div class="dab-widget-placeholder">
                                                        <span class="dashicons dashicons-<?php echo $widget->widget_type === 'table' ? 'list-view' : ($widget->widget_type === 'chart' ? 'chart-bar' : ($widget->widget_type === 'metric' ? 'dashboard' : 'text')); ?>"></span>
                                                        <p><?php echo esc_html($widget->title); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                <?php
                                        }
                                    }
                                }
                                ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php
                    endforeach;
                else:
                    // No layout defined, create a default row with a single column
                ?>
                    <div class="dab-dashboard-row" data-row-id="0">
                        <div class="dab-row-controls">
                            <button type="button" class="dab-row-add-column" title="<?php _e('Add Column', 'db-app-builder'); ?>">
                                <span class="dashicons dashicons-plus"></span>
                            </button>
                            <button type="button" class="dab-row-settings" title="<?php _e('Row Settings', 'db-app-builder'); ?>">
                                <span class="dashicons dashicons-admin-generic"></span>
                            </button>
                            <button type="button" class="dab-row-delete" title="<?php _e('Delete Row', 'db-app-builder'); ?>">
                                <span class="dashicons dashicons-trash"></span>
                            </button>
                        </div>

                        <div class="dab-dashboard-column" data-column-id="0" data-width="12" style="flex: 12 1 0">
                            <div class="dab-column-controls">
                                <button type="button" class="dab-column-settings" title="<?php _e('Column Settings', 'db-app-builder'); ?>">
                                    <span class="dashicons dashicons-admin-generic"></span>
                                </button>
                                <button type="button" class="dab-column-delete" title="<?php _e('Delete Column', 'db-app-builder'); ?>">
                                    <span class="dashicons dashicons-trash"></span>
                                </button>
                            </div>

                            <div class="dab-column-width">12</div>
                            <div class="dab-column-resize-handle"></div>

                            <?php if ($dashboard && !empty($dashboard->widgets)): ?>
                                <?php foreach ($dashboard->widgets as $widget): ?>
                                    <?php
                                    $settings = json_decode($widget->settings, true);
                                    ?>
                                    <div class="dab-dashboard-widget"
                                         data-widget-id="<?php echo $widget->id; ?>"
                                         data-widget-type="<?php echo $widget->widget_type; ?>"
                                         data-row-id="0"
                                         data-column-id="0"
                                         data-width="<?php echo $widget->width; ?>"
                                         data-height="<?php echo $widget->height; ?>">
                                        <div class="dab-widget-header">
                                            <h3 class="dab-widget-title"><?php echo esc_html($widget->title); ?></h3>
                                            <div class="dab-widget-actions">
                                                <button type="button" class="dab-widget-edit" title="<?php _e('Edit Widget', 'db-app-builder'); ?>">
                                                    <span class="dashicons dashicons-edit"></span>
                                                </button>
                                                <button type="button" class="dab-widget-delete" title="<?php _e('Delete Widget', 'db-app-builder'); ?>">
                                                    <span class="dashicons dashicons-trash"></span>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="dab-widget-content">
                                            <div class="dab-widget-placeholder">
                                                <span class="dashicons dashicons-<?php echo $widget->widget_type === 'table' ? 'list-view' : ($widget->widget_type === 'chart' ? 'chart-bar' : ($widget->widget_type === 'metric' ? 'dashboard' : 'text')); ?>"></span>
                                                <p><?php echo esc_html($widget->title); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="dab-empty-column-placeholder">
                                    <span class="dashicons dashicons-plus-alt"></span>
                                    <p><?php _e('Drag widgets here', 'db-app-builder'); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="dab-add-row-container">
                    <div class="dab-add-row-btn">
                        <span class="dashicons dashicons-plus-alt"></span>
                        <?php _e('Add Row', 'db-app-builder'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widget Settings Modal -->
    <div id="widget-settings-modal" class="dab-modal">
        <div class="dab-modal-content">
            <div class="dab-modal-header">
                <h2 id="widget-settings-title"><?php _e('Widget Settings', 'db-app-builder'); ?></h2>
                <button type="button" class="dab-modal-close">&times;</button>
            </div>
            <div class="dab-modal-body">
                <div class="dab-form-group">
                    <label for="widget-title"><?php _e('Title', 'db-app-builder'); ?></label>
                    <input type="text" id="widget-title" class="regular-text" required>
                </div>

                <div id="widget-settings-fields">
                    <!-- Dynamic fields will be added here based on widget type -->
                </div>
            </div>
            <div class="dab-modal-footer">
                <button type="button" id="save-widget" class="button button-primary"><?php _e('Save Widget', 'db-app-builder'); ?></button>
                <button type="button" class="button dab-modal-cancel"><?php _e('Cancel', 'db-app-builder'); ?></button>
            </div>
        </div>
    </div>

    <!-- Row Settings Modal -->
    <div id="row-settings-modal" class="dab-modal">
        <div class="dab-modal-content">
            <div class="dab-modal-header">
                <h2><?php _e('Row Settings', 'db-app-builder'); ?></h2>
                <button type="button" class="dab-modal-close">&times;</button>
            </div>
            <div class="dab-modal-body">
                <div class="dab-form-group">
                    <label for="row-height"><?php _e('Minimum Height (px)', 'db-app-builder'); ?></label>
                    <input type="number" id="row-height" class="regular-text" min="50" value="100">
                </div>

                <div class="dab-form-group">
                    <label for="row-background"><?php _e('Background Color', 'db-app-builder'); ?></label>
                    <input type="text" id="row-background" class="regular-text" placeholder="#f9f9f9">
                    <p class="description"><?php _e('Enter a hex color code or leave blank for default', 'db-app-builder'); ?></p>
                </div>

                <div class="dab-form-group">
                    <label for="row-padding"><?php _e('Padding (px)', 'db-app-builder'); ?></label>
                    <input type="number" id="row-padding" class="regular-text" min="0" value="10">
                </div>

                <div class="dab-form-group">
                    <label for="row-margin-bottom"><?php _e('Bottom Margin (px)', 'db-app-builder'); ?></label>
                    <input type="number" id="row-margin-bottom" class="regular-text" min="0" value="20">
                </div>

                <div class="dab-form-group">
                    <label for="row-border"><?php _e('Border Style', 'db-app-builder'); ?></label>
                    <select id="row-border" class="regular-text">
                        <option value="none"><?php _e('None', 'db-app-builder'); ?></option>
                        <option value="solid"><?php _e('Solid', 'db-app-builder'); ?></option>
                        <option value="dashed" selected><?php _e('Dashed', 'db-app-builder'); ?></option>
                        <option value="dotted"><?php _e('Dotted', 'db-app-builder'); ?></option>
                    </select>
                </div>

                <div class="dab-form-group">
                    <label for="row-border-color"><?php _e('Border Color', 'db-app-builder'); ?></label>
                    <input type="text" id="row-border-color" class="regular-text" placeholder="#cccccc" value="#cccccc">
                </div>

                <div class="dab-form-group">
                    <label for="row-border-width"><?php _e('Border Width (px)', 'db-app-builder'); ?></label>
                    <input type="number" id="row-border-width" class="regular-text" min="0" max="10" value="1">
                </div>

                <div class="dab-form-group">
                    <label for="row-border-radius"><?php _e('Border Radius (px)', 'db-app-builder'); ?></label>
                    <input type="number" id="row-border-radius" class="regular-text" min="0" value="5">
                </div>
            </div>
            <div class="dab-modal-footer">
                <button type="button" id="save-row-settings" class="button button-primary"><?php _e('Save Settings', 'db-app-builder'); ?></button>
                <button type="button" class="button dab-modal-cancel"><?php _e('Cancel', 'db-app-builder'); ?></button>
            </div>
        </div>
    </div>

    <!-- Column Settings Modal -->
    <div id="column-settings-modal" class="dab-modal">
        <div class="dab-modal-content">
            <div class="dab-modal-header">
                <h2><?php _e('Column Settings', 'db-app-builder'); ?></h2>
                <button type="button" class="dab-modal-close">&times;</button>
            </div>
            <div class="dab-modal-body">
                <div class="dab-form-group">
                    <label for="column-width"><?php _e('Width (flex units)', 'db-app-builder'); ?></label>
                    <input type="number" id="column-width" class="regular-text" min="1" max="12" value="1">
                    <p class="description"><?php _e('Relative width compared to other columns (1-12)', 'db-app-builder'); ?></p>
                </div>

                <div class="dab-form-group">
                    <label for="column-background"><?php _e('Background Color', 'db-app-builder'); ?></label>
                    <input type="text" id="column-background" class="regular-text" placeholder="#ffffff">
                    <p class="description"><?php _e('Enter a hex color code or leave blank for default', 'db-app-builder'); ?></p>
                </div>

                <div class="dab-form-group">
                    <label for="column-padding"><?php _e('Padding (px)', 'db-app-builder'); ?></label>
                    <input type="number" id="column-padding" class="regular-text" min="0" value="10">
                </div>

                <div class="dab-form-group">
                    <label for="column-border"><?php _e('Border Style', 'db-app-builder'); ?></label>
                    <select id="column-border" class="regular-text">
                        <option value="none"><?php _e('None', 'db-app-builder'); ?></option>
                        <option value="solid"><?php _e('Solid', 'db-app-builder'); ?></option>
                        <option value="dashed" selected><?php _e('Dashed', 'db-app-builder'); ?></option>
                        <option value="dotted"><?php _e('Dotted', 'db-app-builder'); ?></option>
                    </select>
                </div>

                <div class="dab-form-group">
                    <label for="column-border-color"><?php _e('Border Color', 'db-app-builder'); ?></label>
                    <input type="text" id="column-border-color" class="regular-text" placeholder="#dddddd" value="#dddddd">
                </div>

                <div class="dab-form-group">
                    <label for="column-border-width"><?php _e('Border Width (px)', 'db-app-builder'); ?></label>
                    <input type="number" id="column-border-width" class="regular-text" min="0" max="10" value="1">
                </div>

                <div class="dab-form-group">
                    <label for="column-border-radius"><?php _e('Border Radius (px)', 'db-app-builder'); ?></label>
                    <input type="number" id="column-border-radius" class="regular-text" min="0" value="4">
                </div>
            </div>
            <div class="dab-modal-footer">
                <button type="button" id="save-column-settings" class="button button-primary"><?php _e('Save Settings', 'db-app-builder'); ?></button>
                <button type="button" class="button dab-modal-cancel"><?php _e('Cancel', 'db-app-builder'); ?></button>
            </div>
        </div>
    </div>

    <!-- Dashboard Permissions Modal -->
    <div id="dashboard-permissions-modal" class="dab-modal">
        <div class="dab-modal-content">
            <div class="dab-modal-header">
                <h2><?php _e('Dashboard Permissions', 'db-app-builder'); ?></h2>
                <button type="button" class="dab-modal-close">&times;</button>
            </div>
            <div class="dab-modal-body">
                <p><?php _e('Set which user roles can access this dashboard:', 'db-app-builder'); ?></p>

                <table class="widefat striped">
                    <thead>
                        <tr>
                            <th><?php _e('Role', 'db-app-builder'); ?></th>
                            <th><?php _e('Can View', 'db-app-builder'); ?></th>
                            <th><?php _e('Can Edit', 'db-app-builder'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="dashboard-permissions-table">
                        <?php
                        // Get all WordPress roles
                        $wp_roles = wp_roles();
                        $all_roles = $wp_roles->get_names();

                        // Get current permissions if available
                        $dashboard_permissions = array();
                        if ($dashboard && isset($dashboard->permissions)) {
                            $dashboard_permissions = json_decode($dashboard->permissions, true);
                        }

                        foreach ($all_roles as $role_id => $role_name):
                            // Skip administrator role as they always have access
                            if ($role_id === 'administrator') continue;

                            // Check if this role has permissions set
                            $can_view = isset($dashboard_permissions[$role_id]['can_view']) ? $dashboard_permissions[$role_id]['can_view'] : false;
                            $can_edit = isset($dashboard_permissions[$role_id]['can_edit']) ? $dashboard_permissions[$role_id]['can_edit'] : false;
                        ?>
                        <tr>
                            <td><?php echo esc_html($role_name); ?></td>
                            <td>
                                <input type="checkbox" name="permissions[<?php echo esc_attr($role_id); ?>][can_view]" value="1" <?php checked($can_view); ?>>
                            </td>
                            <td>
                                <input type="checkbox" name="permissions[<?php echo esc_attr($role_id); ?>][can_edit]" value="1" <?php checked($can_edit); ?>>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="dab-modal-footer">
                <button type="button" id="save-dashboard-permissions" class="button button-primary"><?php _e('Save Permissions', 'db-app-builder'); ?></button>
                <button type="button" class="button dab-modal-cancel"><?php _e('Cancel', 'db-app-builder'); ?></button>
            </div>
        </div>
    </div>
</div>
