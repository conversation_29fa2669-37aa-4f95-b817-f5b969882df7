<?php
/**
 * License Manager
 *
 * Handles Envato purchase code validation and domain restriction for the Database App Builder plugin.
 *
 * @package    Database_Admin_Builder
 * @subpackage Database_Admin_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_License_Manager {

    /**
     * The single instance of the class.
     *
     * @var DAB_License_Manager
     */
    protected static $_instance = null;

    /**
     * License data.
     *
     * @var array
     */
    private $license_data = null;

    /**
     * Envato API endpoint for validating purchase codes.
     *
     * @var string
     */
    public $envato_api_url = 'https://api.envato.com/v3/market/author/sale';

    /**
     * Main License Manager Instance.
     *
     * Ensures only one instance of License Manager is loaded or can be loaded.
     *
     * @return DAB_License_Manager - Main instance.
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor.
     */
    public function __construct() {
        // Schedule daily license check
        if (!wp_next_scheduled('dab_daily_license_check')) {
            wp_schedule_event(time(), 'daily', 'dab_daily_license_check');
        }

        add_action('dab_daily_license_check', array($this, 'check_license'));

        // Add admin notices for license status
        add_action('admin_notices', array($this, 'admin_notices'));

        // Initialize license data
        $this->license_data = $this->get_license_data();

        // Register activation/deactivation hooks
        register_activation_hook(DAB_PLUGIN_FILE, array($this, 'activate_plugin'));
        register_deactivation_hook(DAB_PLUGIN_FILE, array($this, 'deactivate_plugin'));
    }

    /**
     * Get license data from database.
     *
     * @return array License data.
     */
    public function get_license_data() {
        if ($this->license_data !== null) {
            return $this->license_data;
        }

        $license_data = get_option('dab_license_data', array(
            'purchase_code' => '',
            'status' => 'inactive',
            'item_id' => '',
            'item_name' => '',
            'buyer' => '',
            'domain' => '',
            'activated_at' => '',
            'last_check' => 0,
        ));

        return $license_data;
    }

    /**
     * Update license data in database.
     *
     * @param array $data License data.
     * @return bool Whether the update was successful.
     */
    public function update_license_data($data) {
        $this->license_data = $data;
        return update_option('dab_license_data', $data);
    }

    /**
     * Get the current domain name.
     *
     * @return string The current domain name.
     */
    private function get_domain_name() {
        return str_replace(array('http://', 'https://'), '', home_url());
    }

    /**
     * Activate license with Envato purchase code.
     *
     * @param string $purchase_code Envato purchase code.
     * @return array|WP_Error Response from Envato API or error.
     */
    public function activate_license($purchase_code) {
        // Sanitize purchase code
        $purchase_code = sanitize_text_field($purchase_code);

        // Validate purchase code format (Envato purchase codes are 36 characters)
        if (strlen($purchase_code) !== 36) {
            return new WP_Error(
                'invalid_purchase_code',
                __('Invalid purchase code format. Envato purchase codes should be 36 characters long.', 'db-app-builder')
            );
        }

        // Get current domain
        $current_domain = $this->get_domain_name();

        // Get existing license data
        $license_data = $this->get_license_data();

        // Check if this purchase code is already activated on a different domain
        if (!empty($license_data['purchase_code']) &&
            $license_data['purchase_code'] === $purchase_code &&
            !empty($license_data['domain']) &&
            $license_data['domain'] !== $current_domain &&
            $license_data['status'] === 'valid') {

            return new WP_Error(
                'domain_mismatch',
                sprintf(
                    __('This purchase code is already activated on %s. Please deactivate it there first or purchase another license.', 'db-app-builder'),
                    $license_data['domain']
                )
            );
        }

        // Verify purchase code with Envato API
        $response = $this->verify_envato_purchase_code($purchase_code);

        if (is_wp_error($response)) {
            return $response;
        }

        // Update license data with Envato purchase information
        $license_data['purchase_code'] = $purchase_code;
        $license_data['status'] = 'valid';
        $license_data['item_id'] = isset($response['item_id']) ? $response['item_id'] : '';
        $license_data['item_name'] = isset($response['item_name']) ? $response['item_name'] : '';
        $license_data['buyer'] = isset($response['buyer']) ? $response['buyer'] : '';
        $license_data['domain'] = $current_domain;
        $license_data['activated_at'] = current_time('mysql');
        $license_data['last_check'] = time();

        $this->update_license_data($license_data);

        return array(
            'success' => true,
            'license' => 'valid',
            'message' => __('Purchase code activated successfully.', 'db-app-builder'),
            'domain' => $current_domain,
            'item_name' => $license_data['item_name'],
            'buyer' => $license_data['buyer']
        );
    }

    /**
     * Verify Envato purchase code.
     *
     * @param string $purchase_code Envato purchase code.
     * @return array|WP_Error Purchase data or error.
     */
    private function verify_envato_purchase_code($purchase_code) {
        // For demonstration purposes, we'll simulate a successful verification
        // In a real implementation, you would make an API call to Envato
        // Note: Envato API requires a personal token which is not included here

        // Simulate API response
        $item_name = 'Database App Builder';
        $item_id = '12345678';
        $buyer = 'Envato Customer';

        // Return simulated purchase data
        return array(
            'item_id' => $item_id,
            'item_name' => $item_name,
            'buyer' => $buyer,
            'purchase_code' => $purchase_code,
            'supported_until' => date('Y-m-d H:i:s', strtotime('+1 year')),
        );

        /*
        // Real implementation would look like this:
        $personal_token = 'YOUR_ENVATO_PERSONAL_TOKEN';

        $response = wp_remote_get(
            $this->envato_api_url . '?code=' . $purchase_code,
            array(
                'timeout' => 15,
                'headers' => array(
                    'Authorization' => 'Bearer ' . $personal_token,
                    'User-Agent' => 'WordPress/' . get_bloginfo('version'),
                ),
            )
        );

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_data = json_decode($response_body, true);

        if ($response_code !== 200) {
            return new WP_Error(
                'envato_api_error',
                __('Failed to verify purchase code with Envato API.', 'db-app-builder'),
                array('response' => $response_body)
            );
        }

        if (empty($response_data)) {
            return new WP_Error(
                'invalid_response',
                __('Invalid response from Envato API.', 'db-app-builder')
            );
        }

        return $response_data;
        */
    }

    /**
     * Deactivate license.
     *
     * @return array|WP_Error Response with deactivation status.
     */
    public function deactivate_license() {
        $license_data = $this->get_license_data();

        if (empty($license_data['purchase_code'])) {
            return new WP_Error('no_license', __('No purchase code has been entered.', 'db-app-builder'));
        }

        // Reset license data
        $license_data['status'] = 'inactive';
        $license_data['domain'] = '';
        $license_data['activated_at'] = '';
        $license_data['last_check'] = time();

        $this->update_license_data($license_data);

        return array(
            'success' => true,
            'license' => 'inactive',
            'message' => __('Purchase code deactivated successfully.', 'db-app-builder')
        );
    }

    /**
     * Check license status.
     *
     * @return array|WP_Error Response with license status.
     */
    public function check_license() {
        $license_data = $this->get_license_data();

        if (empty($license_data['purchase_code'])) {
            return new WP_Error('no_license', __('No purchase code has been entered.', 'db-app-builder'));
        }

        // Don't check more than once per day
        if (time() - $license_data['last_check'] < DAY_IN_SECONDS) {
            return $license_data;
        }

        // Get current domain
        $current_domain = $this->get_domain_name();

        // Check if domain has changed
        if ($license_data['status'] === 'valid' && !empty($license_data['domain']) && $license_data['domain'] !== $current_domain) {
            // Domain has changed, license is no longer valid
            $license_data['status'] = 'invalid';
            $license_data['last_check'] = time();

            $this->update_license_data($license_data);

            return new WP_Error(
                'domain_changed',
                sprintf(
                    __('This purchase code is registered to %s but you are trying to use it on %s. Please deactivate it from the original site or purchase another license.', 'db-app-builder'),
                    $license_data['domain'],
                    $current_domain
                )
            );
        }

        // Verify purchase code with Envato API (optional for daily checks)
        // For performance reasons, we might skip the actual API verification on daily checks
        // and just verify the domain hasn't changed

        // Update last check time
        $license_data['last_check'] = time();
        $this->update_license_data($license_data);

        return array(
            'success' => true,
            'license' => $license_data['status'],
            'domain' => $license_data['domain'],
            'item_name' => $license_data['item_name'],
            'buyer' => $license_data['buyer'],
            'message' => __('License status checked successfully.', 'db-app-builder')
        );
    }

    /**
     * Check if license is valid.
     *
     * @return bool Whether the license is valid.
     */
    public function is_license_valid() {
        $license_data = $this->get_license_data();

        // Check if purchase code exists and status is valid
        if (empty($license_data['purchase_code']) || $license_data['status'] !== 'valid') {
            return false;
        }

        // Get current domain
        $current_domain = $this->get_domain_name();

        // Check if domain matches the registered domain
        if (empty($license_data['domain']) || $license_data['domain'] !== $current_domain) {
            return false;
        }

        return true;
    }

    /**
     * Mask purchase code for display.
     *
     * @param string $purchase_code The purchase code to mask.
     * @return string Masked purchase code.
     */
    public function mask_purchase_code($purchase_code) {
        if (strlen($purchase_code) > 8) {
            return substr($purchase_code, 0, 4) . '****-****-****-****-****' . substr($purchase_code, -4);
        }

        return '****-****-****-****-********';
    }

    /**
     * Display admin notices for license status.
     */
    public function admin_notices() {
        // Only show notices on plugin pages
        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'dab_') === false) {
            return;
        }

        $license_data = $this->get_license_data();
        $current_domain = $this->get_domain_name();

        // Show notice if purchase code is not activated
        if (empty($license_data['purchase_code']) || $license_data['status'] !== 'valid') {
            echo '<div class="notice notice-warning"><p>';
            echo sprintf(
                __('Your Database App Builder is not activated. Please <a href="%s">activate your Envato purchase code</a> to receive updates and support.', 'db-app-builder'),
                admin_url('admin.php?page=dab_license')
            );
            echo '</p></div>';
        }

        // Show notice if domain has changed
        if ($license_data['status'] === 'valid' && !empty($license_data['domain']) && $license_data['domain'] !== $current_domain) {
            echo '<div class="notice notice-error"><p>';
            echo sprintf(
                __('Domain mismatch detected! Your purchase code is registered to %s but you are using it on %s. Please <a href="%s">update your license</a>.', 'db-app-builder'),
                '<strong>' . esc_html($license_data['domain']) . '</strong>',
                '<strong>' . esc_html($current_domain) . '</strong>',
                admin_url('admin.php?page=dab_license')
            );
            echo '</p></div>';
        }
    }

    /**
     * Plugin activation hook.
     */
    public function activate_plugin() {
        // Update plugin version
        update_option('dab_plugin_version', DAB_VERSION);
    }

    /**
     * Plugin deactivation hook.
     */
    public function deactivate_plugin() {
        // Clear scheduled events
        wp_clear_scheduled_hook('dab_daily_license_check');
    }
}

// Initialize License Manager
function DAB_License() {
    return DAB_License_Manager::instance();
}

// Global for backwards compatibility - initialize on init hook to prevent output during activation
function dab_init_license() {
    $GLOBALS['dab_license'] = DAB_License();
}
add_action('init', 'dab_init_license');
