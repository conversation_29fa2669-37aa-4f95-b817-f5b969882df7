<?php
/**
 * Temporary fix for WP Theme Builder plugin errors
 * Add this code to your theme's functions.php file
 */

// Prevent WP Theme Builder from loading problematic scripts on frontend pages
add_action('wp_enqueue_scripts', 'fix_wp_theme_builder_conflicts', 999);

function fix_wp_theme_builder_conflicts() {
    // Check if we're on a frontend page that doesn't need WP Theme Builder
    if (is_page(array('login', 'register', 'user-dashboard', 'user-profile', 'chat'))) {
        
        // Dequeue problematic scripts
        wp_dequeue_script('conditional-logic');
        wp_dequeue_script('popup-builder');
        wp_dequeue_script('mobile-detect');
        
        // Dequeue problematic styles
        wp_dequeue_style('popup-builder');
        
        // Add fallback for MobileDetect if needed
        add_action('wp_footer', 'add_mobile_detect_fallback');
    }
}

function add_mobile_detect_fallback() {
    ?>
    <script>
    // Fallback for MobileDetect if not loaded
    if (typeof MobileDetect === 'undefined') {
        window.MobileDetect = function() {
            return {
                mobile: function() { return false; },
                tablet: function() { return false; },
                phone: function() { return false; }
            };
        };
    }
    </script>
    <?php
}

// Fix autocomplete attributes for password fields
add_action('wp_footer', 'fix_password_autocomplete');

function fix_password_autocomplete() {
    if (is_page(array('login', 'register', 'user-profile'))) {
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add autocomplete attributes to password fields
            const passwordFields = document.querySelectorAll('input[type="password"]');
            passwordFields.forEach(function(field) {
                if (field.name === 'password' && field.closest('form').querySelector('input[name="username"]')) {
                    field.setAttribute('autocomplete', 'current-password');
                } else if (field.name === 'password' || field.name === 'new_password') {
                    field.setAttribute('autocomplete', 'new-password');
                } else if (field.name === 'confirm_password') {
                    field.setAttribute('autocomplete', 'new-password');
                }
            });
        });
        </script>
        <?php
    }
}
