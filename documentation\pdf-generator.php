<?php
/**
 * PDF Documentation Generator
 *
 * This file generates a PDF version of the documentation.
 * It requires the TCPDF library, which is not included with the plugin.
 * You'll need to install it separately or use a different PDF library.
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/documentation
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Generate PDF documentation
 *
 * @return string Path to the generated PDF file
 */
function dab_generate_pdf_documentation() {
    // Check if TCPDF is available
    if (!class_exists('TCPDF')) {
        // If TCPDF is not available, try to load it from a common location
        $tcpdf_path = WP_CONTENT_DIR . '/plugins/tcpdf/tcpdf.php';
        if (file_exists($tcpdf_path)) {
            require_once($tcpdf_path);
        } else {
            return false;
        }
    }

    // Create new PDF document
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

    // Set document information
    $pdf->SetCreator('Database App Builder');
    $pdf->SetAuthor('Your Name');
    $pdf->SetTitle('Database App Builder Documentation');
    $pdf->SetSubject('Database App Builder Documentation');
    $pdf->SetKeywords('Database, App Builder, WordPress, Plugin, Documentation');

    // Set default header data
    $pdf->SetHeaderData('', 0, 'Database App Builder Documentation', 'Version 1.0.4');

    // Set header and footer fonts
    $pdf->setHeaderFont(Array('helvetica', '', 10));
    $pdf->setFooterFont(Array('helvetica', '', 8));

    // Set default monospaced font
    $pdf->SetDefaultMonospacedFont('courier');

    // Set margins
    $pdf->SetMargins(15, 20, 15);
    $pdf->SetHeaderMargin(5);
    $pdf->SetFooterMargin(10);

    // Set auto page breaks
    $pdf->SetAutoPageBreak(TRUE, 15);

    // Set image scale factor
    $pdf->setImageScale(1.25);

    // Set font
    $pdf->SetFont('helvetica', '', 10);

    // Add a page
    $pdf->AddPage();

    // Get the HTML content from the documentation file
    $html_file = plugin_dir_path(__FILE__) . 'index.html';
    if (!file_exists($html_file)) {
        return false;
    }

    $html = file_get_contents($html_file);

    // Extract the body content
    preg_match('/<body>(.*?)<\/body>/s', $html, $matches);
    $body_content = isset($matches[1]) ? $matches[1] : '';

    // Clean up the HTML for PDF
    $body_content = str_replace('<div class="toc">', '<h2>Table of Contents</h2><div class="toc">', $body_content);
    $body_content = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $body_content);

    // Write HTML content
    $pdf->writeHTML($body_content, true, false, true, false, '');

    // Close and output PDF document
    $pdf_file = plugin_dir_path(__FILE__) . 'database-app-builder-documentation.pdf';
    $pdf->Output($pdf_file, 'F');

    return $pdf_file;
}

/**
 * Add PDF documentation link to the admin page
 */
function dab_add_pdf_documentation_link() {
    // Only add the link on the documentation page
    $screen = get_current_screen();
    if (!$screen || $screen->id !== 'dab_documentation') {
        return;
    }

    // Generate the PDF if it doesn't exist
    $pdf_file = plugin_dir_path(__FILE__) . 'database-app-builder-documentation.pdf';
    if (!file_exists($pdf_file)) {
        $pdf_file = dab_generate_pdf_documentation();
    }

    // Add the link if the PDF exists
    if ($pdf_file) {
        $pdf_url = plugin_dir_url(__FILE__) . 'database-app-builder-documentation.pdf';
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p>' . __('You can download a PDF version of this documentation for offline reading: ', 'db-app-builder');
        echo '<a href="' . esc_url($pdf_url) . '" target="_blank">' . __('Download PDF', 'db-app-builder') . '</a></p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'dab_add_pdf_documentation_link');
