/**
 * Chat System JavaScript
 *
 * Handles real-time chat functionality
 */

jQuery(document).ready(function($) {
    'use strict';

    // Chat state
    let currentChat = {
        type: null, // 'user' or 'group'
        id: null,
        name: '',
        lastMessageId: 0
    };

    let refreshInterval = null;
    let typingTimeout = null;
    let isTyping = false;

    // Initialize chat
    initializeChat();

    function initializeChat() {
        loadConversations();
        setupEventHandlers();
        startRefreshInterval();
        updateUserStatus('online');

        // Set default view
        switchView(dab_chat.settings.default_view);
    }

    // Setup event handlers
    function setupEventHandlers() {
        // Navigation
        $('.dab-chat-nav-btn').on('click', function() {
            const view = $(this).data('view');
            switchView(view);
        });

        // Search
        $('#dab-chat-search').on('input', debounce(function() {
            const query = $(this).val();
            const currentView = $('.dab-chat-nav-btn.active').data('view');
            performSearch(currentView, query);
        }, 300));

        // New chat
        $('#dab-new-chat').on('click', openNewChatModal);
        $('#dab-close-new-chat, #dab-cancel-create-group').on('click', closeModals);

        // Create group
        $('#dab-create-group-form').on('submit', handleCreateGroup);
        $('#dab-close-create-group').on('click', closeModals);

        // Message input
        $('#dab-chat-input').on('keydown', handleMessageInput);
        $('#dab-chat-input').on('input', handleTyping);
        $('#dab-chat-send').on('click', sendMessage);

        // Chat actions
        $('#dab-chat-close').on('click', closeChat);
        $(document).on('click', '.dab-chat-item', handleChatItemClick);
        $(document).on('click', '.dab-message-delete', handleDeleteMessage);

        // Modal close on outside click
        $('.dab-chat-modal').on('click', function(e) {
            if (e.target === this) {
                closeModals();
            }
        });

        // Auto-resize textarea
        $('#dab-chat-input').on('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Update last seen periodically
        setInterval(function() {
            updateLastSeen();
        }, 30000); // Every 30 seconds
    }

    // Switch between different views
    function switchView(view) {
        $('.dab-chat-nav-btn').removeClass('active');
        $(`.dab-chat-nav-btn[data-view="${view}"]`).addClass('active');

        $('.dab-chat-list').hide();
        $(`#dab-${view}-list`).show();

        // Update search placeholder
        const placeholders = {
            conversations: dab_chat.messages.search_users,
            groups: dab_chat.messages.search_groups,
            users: dab_chat.messages.search_users
        };
        $('#dab-chat-search').attr('placeholder', placeholders[view] || 'Search...');

        // Load data for the view
        switch(view) {
            case 'conversations':
                loadConversations();
                break;
            case 'groups':
                loadGroups();
                break;
            case 'users':
                loadUsers();
                break;
        }
    }

    // Load conversations
    function loadConversations() {
        const $list = $('#dab-conversations-list');
        $list.html('<div class="dab-chat-loading"><span class="dab-chat-spinner"></span>' + dab_chat.messages.loading + '</div>');

        $.post(dab_chat.ajax_url, {
            action: 'dab_get_conversations',
            nonce: dab_chat.nonce
        })
        .done(function(response) {
            if (response.success) {
                displayConversations(response.data);
            } else {
                showError($list, response.data || dab_chat.messages.error);
            }
        })
        .fail(function() {
            showError($list, dab_chat.messages.error);
        });
    }

    // Display conversations
    function displayConversations(conversations) {
        const $list = $('#dab-conversations-list');

        if (conversations.length === 0) {
            $list.html('<div class="dab-chat-empty">' + dab_chat.messages.no_conversations + '</div>');
            return;
        }

        let html = '';
        conversations.forEach(function(conv) {
            const avatar = conv.other_avatar ?
                `<img src="${conv.other_avatar}" alt="${conv.other_username}">` :
                `<div class="dab-chat-avatar-placeholder">${conv.other_username.charAt(0).toUpperCase()}</div>`;

            const lastMessage = conv.last_message ?
                (conv.last_sender_id == dab_chat.current_user.id ? 'You: ' : '') + truncateText(conv.last_message, 50) :
                'No messages yet';

            const unreadBadge = conv.unread_count > 0 ?
                `<span class="dab-chat-unread-badge">${conv.unread_count}</span>` : '';

            html += `
                <div class="dab-chat-item" data-type="user" data-id="${conv.other_user_id}" data-name="${conv.other_username}">
                    <div class="dab-chat-avatar">${avatar}</div>
                    <div class="dab-chat-item-content">
                        <div class="dab-chat-item-header">
                            <span class="dab-chat-item-name">${conv.other_first_name} ${conv.other_last_name}</span>
                            <span class="dab-chat-item-time">${formatTime(conv.last_activity)}</span>
                        </div>
                        <div class="dab-chat-item-message">${lastMessage}</div>
                    </div>
                    ${unreadBadge}
                </div>
            `;
        });

        $list.html(html);
    }

    // Load groups
    function loadGroups() {
        const $list = $('#dab-groups-list');
        $list.html('<div class="dab-chat-loading"><span class="dab-chat-spinner"></span>' + dab_chat.messages.loading + '</div>');

        $.post(dab_chat.ajax_url, {
            action: 'dab_get_groups',
            nonce: dab_chat.nonce,
            type: 'user'
        })
        .done(function(response) {
            if (response.success) {
                displayGroups(response.data);
            } else {
                showError($list, response.data || dab_chat.messages.error);
            }
        })
        .fail(function() {
            showError($list, dab_chat.messages.error);
        });
    }

    // Display groups
    function displayGroups(groups) {
        const $list = $('#dab-groups-list');

        if (groups.length === 0) {
            $list.html(`
                <div class="dab-chat-empty">
                    ${dab_chat.messages.no_groups}
                    <button type="button" class="dab-chat-btn dab-chat-btn-primary dab-chat-btn-sm" onclick="openCreateGroupModal()">
                        ${dab_chat.messages.create_group}
                    </button>
                </div>
            `);
            return;
        }

        let html = '';
        groups.forEach(function(group) {
            const avatar = group.avatar_url ?
                `<img src="${group.avatar_url}" alt="${group.name}">` :
                `<div class="dab-chat-avatar-placeholder">${group.name.charAt(0).toUpperCase()}</div>`;

            const unreadBadge = group.unread_count > 0 ?
                `<span class="dab-chat-unread-badge">${group.unread_count}</span>` : '';

            html += `
                <div class="dab-chat-item" data-type="group" data-id="${group.id}" data-name="${group.name}">
                    <div class="dab-chat-avatar">${avatar}</div>
                    <div class="dab-chat-item-content">
                        <div class="dab-chat-item-header">
                            <span class="dab-chat-item-name">${group.name}</span>
                            <span class="dab-chat-item-members">${group.member_count} members</span>
                        </div>
                        <div class="dab-chat-item-message">${group.last_message || 'No messages yet'}</div>
                    </div>
                    ${unreadBadge}
                </div>
            `;
        });

        $list.html(html);
    }

    // Load users
    function loadUsers() {
        const $list = $('#dab-users-list');
        $list.html('<div class="dab-chat-loading"><span class="dab-chat-spinner"></span>' + dab_chat.messages.loading + '</div>');

        // For now, show empty state with search instruction
        $list.html(`
            <div class="dab-chat-empty">
                <p>Search for users to start a conversation</p>
            </div>
        `);
    }

    // Handle chat item click
    function handleChatItemClick() {
        const $item = $(this);
        const type = $item.data('type');
        const id = $item.data('id');
        const name = $item.data('name');

        openChat(type, id, name);

        // Mark as active
        $('.dab-chat-item').removeClass('active');
        $item.addClass('active');

        // Clear unread badge
        $item.find('.dab-chat-unread-badge').remove();
    }

    // Open chat
    function openChat(type, id, name) {
        currentChat = { type, id, name, lastMessageId: 0 };

        // Update header
        updateChatHeader(type, id, name);

        // Show chat area
        $('#dab-chat-welcome').hide();
        $('#dab-chat-area').show();

        // Load messages
        loadMessages();

        // Focus input
        $('#dab-chat-input').focus();
    }

    // Update chat header
    function updateChatHeader(type, id, name) {
        const $avatar = $('#dab-chat-header-avatar');
        const $name = $('#dab-chat-header-name');
        const $status = $('#dab-chat-header-status');

        $avatar.html(`<div class="dab-chat-avatar-placeholder">${name.charAt(0).toUpperCase()}</div>`);
        $name.text(name);

        if (type === 'group') {
            $status.text('Group');
        } else {
            $status.text('Online'); // TODO: Get actual status
        }
    }

    // Load messages
    function loadMessages() {
        const $messages = $('#dab-chat-messages');
        $messages.html('<div class="dab-chat-loading"><span class="dab-chat-spinner"></span>' + dab_chat.messages.loading + '</div>');

        const data = {
            action: 'dab_get_messages',
            nonce: dab_chat.nonce,
            limit: 50
        };

        if (currentChat.type === 'group') {
            data.group_id = currentChat.id;
        } else {
            data.recipient_id = currentChat.id;
        }

        $.post(dab_chat.ajax_url, data)
        .done(function(response) {
            if (response.success) {
                displayMessages(response.data);
                if (response.data.length > 0) {
                    currentChat.lastMessageId = response.data[response.data.length - 1].id;
                }
            } else {
                showError($messages, response.data || dab_chat.messages.error);
            }
        })
        .fail(function() {
            showError($messages, dab_chat.messages.error);
        });
    }

    // Display messages
    function displayMessages(messages) {
        const $messages = $('#dab-chat-messages');

        if (messages.length === 0) {
            $messages.html('<div class="dab-chat-empty">' + dab_chat.messages.no_messages + '</div>');
            return;
        }

        let html = '';
        let lastDate = '';

        messages.forEach(function(message) {
            const messageDate = formatDate(message.created_at);

            // Add date separator
            if (messageDate !== lastDate) {
                html += `<div class="dab-chat-date-separator">${messageDate}</div>`;
                lastDate = messageDate;
            }

            const isOwn = message.sender_id == dab_chat.current_user.id;
            const avatar = message.sender_avatar ?
                `<img src="${message.sender_avatar}" alt="${message.sender_username}">` :
                `<div class="dab-chat-avatar-placeholder">${message.sender_username.charAt(0).toUpperCase()}</div>`;

            const deleteBtn = isOwn ?
                `<button type="button" class="dab-message-delete" data-id="${message.id}" title="Delete message">
                    <span class="dashicons dashicons-trash"></span>
                </button>` : '';

            html += `
                <div class="dab-chat-message ${isOwn ? 'own' : ''}">
                    ${!isOwn ? `<div class="dab-chat-avatar">${avatar}</div>` : ''}
                    <div class="dab-chat-message-content">
                        ${!isOwn ? `<div class="dab-chat-message-sender">${message.sender_first_name} ${message.sender_last_name}</div>` : ''}
                        <div class="dab-chat-message-text">${escapeHtml(message.message)}</div>
                        <div class="dab-chat-message-time">
                            ${formatTime(message.created_at)}
                            ${deleteBtn}
                        </div>
                    </div>
                </div>
            `;
        });

        $messages.html(html);
        scrollToBottom();
    }

    // Handle message input
    function handleMessageInput(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    }

    // Handle typing indicator
    function handleTyping() {
        if (!isTyping) {
            isTyping = true;
            // TODO: Send typing indicator
        }

        clearTimeout(typingTimeout);
        typingTimeout = setTimeout(function() {
            isTyping = false;
            // TODO: Stop typing indicator
        }, dab_chat.settings.typing_timeout);
    }

    // Send message
    function sendMessage() {
        const $input = $('#dab-chat-input');
        const message = $input.val().trim();

        if (!message || !currentChat.id) {
            return;
        }

        const data = {
            action: 'dab_send_message',
            nonce: dab_chat.nonce,
            message: message
        };

        if (currentChat.type === 'group') {
            data.group_id = currentChat.id;
        } else {
            data.recipient_id = currentChat.id;
        }

        // Clear input
        $input.val('').css('height', 'auto');

        // Send message
        $.post(dab_chat.ajax_url, data)
        .done(function(response) {
            if (response.success) {
                // Add message to chat
                appendMessage(response.data.data);
                currentChat.lastMessageId = response.data.data.id;
            } else {
                alert(response.data || dab_chat.messages.error);
                $input.val(message); // Restore message
            }
        })
        .fail(function() {
            alert(dab_chat.messages.error);
            $input.val(message); // Restore message
        });
    }

    // Append new message
    function appendMessage(message) {
        const $messages = $('#dab-chat-messages');

        // Remove empty state if present
        $messages.find('.dab-chat-empty').remove();

        const isOwn = message.sender_id == dab_chat.current_user.id;
        const avatar = message.sender_avatar ?
            `<img src="${message.sender_avatar}" alt="${message.sender_username}">` :
            `<div class="dab-chat-avatar-placeholder">${message.sender_username.charAt(0).toUpperCase()}</div>`;

        const deleteBtn = isOwn ?
            `<button type="button" class="dab-message-delete" data-id="${message.id}" title="Delete message">
                <span class="dashicons dashicons-trash"></span>
            </button>` : '';

        const messageHtml = `
            <div class="dab-chat-message ${isOwn ? 'own' : ''}">
                ${!isOwn ? `<div class="dab-chat-avatar">${avatar}</div>` : ''}
                <div class="dab-chat-message-content">
                    ${!isOwn ? `<div class="dab-chat-message-sender">${message.sender_first_name} ${message.sender_last_name}</div>` : ''}
                    <div class="dab-chat-message-text">${escapeHtml(message.message)}</div>
                    <div class="dab-chat-message-time">
                        ${formatTime(message.created_at)}
                        ${deleteBtn}
                    </div>
                </div>
            </div>
        `;

        $messages.append(messageHtml);
        scrollToBottom();
    }

    // Handle delete message
    function handleDeleteMessage() {
        if (!confirm(dab_chat.messages.confirm_delete)) {
            return;
        }

        const messageId = $(this).data('id');
        const $message = $(this).closest('.dab-chat-message');

        $.post(dab_chat.ajax_url, {
            action: 'dab_delete_message',
            nonce: dab_chat.nonce,
            message_id: messageId
        })
        .done(function(response) {
            if (response.success) {
                $message.fadeOut(300, function() {
                    $(this).remove();
                });
            } else {
                alert(response.data || dab_chat.messages.error);
            }
        })
        .fail(function() {
            alert(dab_chat.messages.error);
        });
    }

    // Close chat
    function closeChat() {
        currentChat = { type: null, id: null, name: '', lastMessageId: 0 };
        $('#dab-chat-area').hide();
        $('#dab-chat-welcome').show();
        $('.dab-chat-item').removeClass('active');
    }

    // Open new chat modal
    function openNewChatModal() {
        $('#dab-new-chat-modal').show();
        $('#dab-new-chat-search').focus();
    }

    // Open create group modal
    window.openCreateGroupModal = function() {
        $('#dab-create-group-modal').show();
        $('#dab-group-name').focus();
    };

    // Close modals
    function closeModals() {
        $('.dab-chat-modal').hide();
        $('.dab-chat-modal input, .dab-chat-modal textarea').val('');
    }

    // Handle create group
    function handleCreateGroup(e) {
        e.preventDefault();

        const formData = {
            action: 'dab_create_group',
            nonce: dab_chat.nonce,
            name: $('#dab-group-name').val(),
            description: $('#dab-group-description').val(),
            group_type: $('#dab-group-type').val()
        };

        if (!formData.name) {
            alert('Group name is required');
            return;
        }

        $.post(dab_chat.ajax_url, formData)
        .done(function(response) {
            if (response.success) {
                closeModals();
                loadGroups(); // Refresh groups list
                alert('Group created successfully!');
            } else {
                alert(response.data || dab_chat.messages.error);
            }
        })
        .fail(function() {
            alert(dab_chat.messages.error);
        });
    }

    // Perform search
    function performSearch(view, query) {
        if (query.length < 2) {
            // Reset to default view
            switch(view) {
                case 'conversations':
                    loadConversations();
                    break;
                case 'groups':
                    loadGroups();
                    break;
                case 'users':
                    loadUsers();
                    break;
            }
            return;
        }

        if (view === 'users' || view === 'conversations') {
            searchUsers(query);
        } else if (view === 'groups') {
            searchGroups(query);
        }
    }

    // Search users
    function searchUsers(query) {
        $.post(dab_chat.ajax_url, {
            action: 'dab_search_users',
            nonce: dab_chat.nonce,
            search: query
        })
        .done(function(response) {
            if (response.success) {
                displaySearchUsers(response.data);
            }
        });
    }

    // Display search users
    function displaySearchUsers(users) {
        const $list = $('#dab-users-list');

        if (users.length === 0) {
            $list.html('<div class="dab-chat-empty">No users found</div>');
            return;
        }

        let html = '';
        users.forEach(function(user) {
            const avatar = user.avatar_url ?
                `<img src="${user.avatar_url}" alt="${user.username}">` :
                `<div class="dab-chat-avatar-placeholder">${user.username.charAt(0).toUpperCase()}</div>`;

            html += `
                <div class="dab-chat-item" data-type="user" data-id="${user.id}" data-name="${user.username}">
                    <div class="dab-chat-avatar">${avatar}</div>
                    <div class="dab-chat-item-content">
                        <div class="dab-chat-item-header">
                            <span class="dab-chat-item-name">${user.first_name} ${user.last_name}</span>
                            <span class="dab-chat-item-username">@${user.username}</span>
                        </div>
                        <div class="dab-chat-item-message">Click to start chatting</div>
                    </div>
                </div>
            `;
        });

        $list.html(html);
    }

    // Search groups
    function searchGroups(query) {
        $.post(dab_chat.ajax_url, {
            action: 'dab_get_groups',
            nonce: dab_chat.nonce,
            type: 'public',
            search: query
        })
        .done(function(response) {
            if (response.success) {
                displayGroups(response.data);
            }
        });
    }

    // Start refresh interval
    function startRefreshInterval() {
        refreshInterval = setInterval(function() {
            if (currentChat.id) {
                checkNewMessages();
            }
            updateConversationsList();
        }, dab_chat.settings.refresh_interval);
    }

    // Check for new messages
    function checkNewMessages() {
        const data = {
            action: 'dab_get_messages',
            nonce: dab_chat.nonce,
            limit: 10,
            since_id: currentChat.lastMessageId
        };

        if (currentChat.type === 'group') {
            data.group_id = currentChat.id;
        } else {
            data.recipient_id = currentChat.id;
        }

        $.post(dab_chat.ajax_url, data)
        .done(function(response) {
            if (response.success && response.data.length > 0) {
                response.data.forEach(function(message) {
                    appendMessage(message);
                    currentChat.lastMessageId = message.id;
                });
            }
        });
    }

    // Update conversations list
    function updateConversationsList() {
        if ($('.dab-chat-nav-btn.active').data('view') === 'conversations') {
            loadConversations();
        }
    }

    // Update user status
    function updateUserStatus(status) {
        $.post(dab_chat.ajax_url, {
            action: 'dab_update_user_status',
            nonce: dab_chat.nonce,
            status: status
        });
    }

    // Update last seen
    function updateLastSeen() {
        $.post(dab_chat.ajax_url, {
            action: 'dab_update_last_seen',
            nonce: dab_chat.nonce
        });
    }

    // Utility functions
    function scrollToBottom() {
        const $messages = $('#dab-chat-messages');
        $messages.scrollTop($messages[0].scrollHeight);
    }

    function formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now - date) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (diffInHours < 168) { // 7 days
            return date.toLocaleDateString([], { weekday: 'short' });
        } else {
            return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

        if (diffInDays === 0) {
            return 'Today';
        } else if (diffInDays === 1) {
            return 'Yesterday';
        } else if (diffInDays < 7) {
            return date.toLocaleDateString([], { weekday: 'long' });
        } else {
            return date.toLocaleDateString([], { month: 'long', day: 'numeric', year: 'numeric' });
        }
    }

    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function showError($container, message) {
        $container.html(`<div class="dab-chat-error">${message}</div>`);
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Cleanup on page unload
    $(window).on('beforeunload', function() {
        updateUserStatus('offline');
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    });
});
